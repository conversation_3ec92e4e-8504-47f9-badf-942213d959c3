#!/usr/bin/env python3
"""
🌿 Medicinal Plant Recognition - Comprehensive Evaluation Metrics
Advanced evaluation framework with confusion matrix analysis and misclassification detection
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    classification_report, confusion_matrix, accuracy_score,
    precision_recall_fscore_support, top_k_accuracy_score
)
from sklearn.preprocessing import label_binarize
from sklearn.metrics import roc_curve, auc
import tensorflow as tf
from typing import Dict, List, Tuple, Optional
import json
import os
from datetime import datetime

class PlantEvaluationMetrics:
    """Comprehensive evaluation system for medicinal plant recognition"""
    
    def __init__(self, class_names: List[str], model_name: str = "plant_model"):
        self.class_names = class_names
        self.num_classes = len(class_names)
        self.model_name = model_name
        self.evaluation_results = {}
        
    def evaluate_model(self, model, test_generator, save_results: bool = True) -> Dict:
        """Comprehensive model evaluation with all metrics"""
        print("🔬 Starting comprehensive model evaluation...")
        
        # Get predictions and true labels
        predictions = model.predict(test_generator, verbose=1)
        predicted_classes = np.argmax(predictions, axis=1)
        true_classes = test_generator.classes
        
        # Calculate all metrics
        results = {
            'basic_metrics': self._calculate_basic_metrics(true_classes, predicted_classes, predictions),
            'confusion_matrix': self._analyze_confusion_matrix(true_classes, predicted_classes),
            'confidence_analysis': self._analyze_confidence(predictions, true_classes),
            'per_class_metrics': self._calculate_per_class_metrics(true_classes, predicted_classes),
            'misclassification_analysis': self._analyze_misclassifications(
                true_classes, predicted_classes, predictions, test_generator
            ),
            'similarity_analysis': self._analyze_plant_similarities(true_classes, predicted_classes),
            'roc_analysis': self._calculate_roc_metrics(true_classes, predictions)
        }
        
        self.evaluation_results = results
        
        if save_results:
            self._save_evaluation_results(results)
            self._generate_evaluation_report(results)
        
        return results
    
    def _calculate_basic_metrics(self, y_true, y_pred, y_pred_proba) -> Dict:
        """Calculate basic classification metrics"""
        accuracy = accuracy_score(y_true, y_pred)
        top_3_accuracy = top_k_accuracy_score(y_true, y_pred_proba, k=3)
        top_5_accuracy = top_k_accuracy_score(y_true, y_pred_proba, k=5)
        
        # Per-class precision, recall, f1
        precision, recall, f1, support = precision_recall_fscore_support(
            y_true, y_pred, average=None, zero_division=0
        )
        
        # Macro and weighted averages
        macro_precision = np.mean(precision)
        macro_recall = np.mean(recall)
        macro_f1 = np.mean(f1)
        
        weighted_precision = np.average(precision, weights=support)
        weighted_recall = np.average(recall, weights=support)
        weighted_f1 = np.average(f1, weights=support)
        
        return {
            'accuracy': accuracy,
            'top_3_accuracy': top_3_accuracy,
            'top_5_accuracy': top_5_accuracy,
            'macro_precision': macro_precision,
            'macro_recall': macro_recall,
            'macro_f1': macro_f1,
            'weighted_precision': weighted_precision,
            'weighted_recall': weighted_recall,
            'weighted_f1': weighted_f1,
            'per_class_precision': precision.tolist(),
            'per_class_recall': recall.tolist(),
            'per_class_f1': f1.tolist(),
            'per_class_support': support.tolist()
        }
    
    def _analyze_confusion_matrix(self, y_true, y_pred) -> Dict:
        """Detailed confusion matrix analysis"""
        cm = confusion_matrix(y_true, y_pred)
        
        # Normalize confusion matrix
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        
        # Find most confused pairs
        confused_pairs = []
        for i in range(self.num_classes):
            for j in range(self.num_classes):
                if i != j and cm[i, j] > 0:
                    confusion_rate = cm_normalized[i, j]
                    confused_pairs.append({
                        'true_class': self.class_names[i],
                        'predicted_class': self.class_names[j],
                        'count': int(cm[i, j]),
                        'confusion_rate': float(confusion_rate)
                    })
        
        # Sort by confusion rate
        confused_pairs.sort(key=lambda x: x['confusion_rate'], reverse=True)
        
        # Generate confusion matrix plot
        self._plot_confusion_matrix(cm, cm_normalized)
        
        return {
            'confusion_matrix': cm.tolist(),
            'normalized_confusion_matrix': cm_normalized.tolist(),
            'most_confused_pairs': confused_pairs[:20],  # Top 20 confused pairs
            'total_misclassifications': int(np.sum(cm) - np.trace(cm))
        }
    
    def _analyze_confidence(self, predictions, y_true) -> Dict:
        """Analyze prediction confidence patterns"""
        max_confidences = np.max(predictions, axis=1)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # Overall confidence statistics
        mean_confidence = np.mean(max_confidences)
        std_confidence = np.std(max_confidences)
        
        # Confidence by correctness
        correct_mask = (predicted_classes == y_true)
        correct_confidences = max_confidences[correct_mask]
        incorrect_confidences = max_confidences[~correct_mask]
        
        # Confidence thresholds analysis
        thresholds = [0.5, 0.6, 0.7, 0.8, 0.9, 0.95]
        threshold_analysis = {}
        
        for threshold in thresholds:
            high_conf_mask = max_confidences >= threshold
            if np.sum(high_conf_mask) > 0:
                high_conf_accuracy = np.mean(correct_mask[high_conf_mask])
                coverage = np.mean(high_conf_mask)
            else:
                high_conf_accuracy = 0.0
                coverage = 0.0
            
            threshold_analysis[threshold] = {
                'accuracy': float(high_conf_accuracy),
                'coverage': float(coverage),
                'count': int(np.sum(high_conf_mask))
            }
        
        # Per-class confidence analysis
        per_class_confidence = {}
        for class_idx in range(self.num_classes):
            class_mask = (y_true == class_idx)
            if np.sum(class_mask) > 0:
                class_confidences = max_confidences[class_mask]
                class_correct = correct_mask[class_mask]
                
                per_class_confidence[self.class_names[class_idx]] = {
                    'mean_confidence': float(np.mean(class_confidences)),
                    'accuracy': float(np.mean(class_correct)),
                    'high_confidence_rate': float(np.mean(class_confidences >= 0.8))
                }
        
        return {
            'overall_mean_confidence': float(mean_confidence),
            'overall_std_confidence': float(std_confidence),
            'correct_mean_confidence': float(np.mean(correct_confidences)) if len(correct_confidences) > 0 else 0.0,
            'incorrect_mean_confidence': float(np.mean(incorrect_confidences)) if len(incorrect_confidences) > 0 else 0.0,
            'threshold_analysis': threshold_analysis,
            'per_class_confidence': per_class_confidence,
            'recommended_threshold': self._find_optimal_threshold(max_confidences, correct_mask)
        }
    
    def _find_optimal_threshold(self, confidences, correct_mask) -> float:
        """Find optimal confidence threshold balancing accuracy and coverage"""
        thresholds = np.arange(0.5, 1.0, 0.05)
        best_score = 0
        best_threshold = 0.8
        
        for threshold in thresholds:
            high_conf_mask = confidences >= threshold
            if np.sum(high_conf_mask) > 0:
                accuracy = np.mean(correct_mask[high_conf_mask])
                coverage = np.mean(high_conf_mask)
                # Balance accuracy and coverage (weighted toward accuracy)
                score = 0.7 * accuracy + 0.3 * coverage
                
                if score > best_score:
                    best_score = score
                    best_threshold = threshold
        
        return float(best_threshold)
    
    def _calculate_per_class_metrics(self, y_true, y_pred) -> Dict:
        """Calculate detailed metrics for each plant class"""
        report = classification_report(
            y_true, y_pred, 
            target_names=self.class_names, 
            output_dict=True,
            zero_division=0
        )
        
        # Identify problematic classes
        problematic_classes = []
        for class_name in self.class_names:
            if class_name in report:
                f1_score = report[class_name]['f1-score']
                if f1_score < 0.7:  # Threshold for problematic classes
                    problematic_classes.append({
                        'class': class_name,
                        'f1_score': f1_score,
                        'precision': report[class_name]['precision'],
                        'recall': report[class_name]['recall'],
                        'support': report[class_name]['support']
                    })
        
        problematic_classes.sort(key=lambda x: x['f1_score'])
        
        return {
            'classification_report': report,
            'problematic_classes': problematic_classes
        }
    
    def _analyze_misclassifications(self, y_true, y_pred, y_pred_proba, test_generator) -> Dict:
        """Detailed analysis of misclassified samples"""
        misclassified_indices = np.where(y_true != y_pred)[0]
        
        misclassifications = []
        for idx in misclassified_indices[:50]:  # Analyze top 50 misclassifications
            true_class = self.class_names[y_true[idx]]
            pred_class = self.class_names[y_pred[idx]]
            confidence = float(y_pred_proba[idx, y_pred[idx]])
            true_confidence = float(y_pred_proba[idx, y_true[idx]])
            
            # Get top 3 predictions
            top_3_indices = np.argsort(y_pred_proba[idx])[-3:][::-1]
            top_3_predictions = [
                {
                    'class': self.class_names[i],
                    'confidence': float(y_pred_proba[idx, i])
                }
                for i in top_3_indices
            ]
            
            misclassifications.append({
                'sample_index': int(idx),
                'true_class': true_class,
                'predicted_class': pred_class,
                'prediction_confidence': confidence,
                'true_class_confidence': true_confidence,
                'confidence_gap': confidence - true_confidence,
                'top_3_predictions': top_3_predictions,
                'filename': test_generator.filenames[idx] if hasattr(test_generator, 'filenames') else f"sample_{idx}"
            })
        
        # Sort by confidence gap (most confident wrong predictions first)
        misclassifications.sort(key=lambda x: x['confidence_gap'], reverse=True)
        
        return {
            'total_misclassifications': len(misclassified_indices),
            'misclassification_rate': len(misclassified_indices) / len(y_true),
            'detailed_misclassifications': misclassifications
        }
    
    def _analyze_plant_similarities(self, y_true, y_pred) -> Dict:
        """Analyze which plants are commonly confused with each other"""
        cm = confusion_matrix(y_true, y_pred)
        
        # Create similarity matrix based on confusion
        similarity_matrix = np.zeros((self.num_classes, self.num_classes))
        for i in range(self.num_classes):
            for j in range(self.num_classes):
                if i != j:
                    # Bidirectional confusion rate
                    conf_ij = cm[i, j] / max(np.sum(cm[i, :]), 1)
                    conf_ji = cm[j, i] / max(np.sum(cm[j, :]), 1)
                    similarity_matrix[i, j] = (conf_ij + conf_ji) / 2
        
        # Find most similar plant pairs
        similar_pairs = []
        for i in range(self.num_classes):
            for j in range(i + 1, self.num_classes):
                similarity = similarity_matrix[i, j]
                if similarity > 0.05:  # Threshold for significant similarity
                    similar_pairs.append({
                        'plant_1': self.class_names[i],
                        'plant_2': self.class_names[j],
                        'similarity_score': float(similarity),
                        'confusion_count': int(cm[i, j] + cm[j, i])
                    })
        
        similar_pairs.sort(key=lambda x: x['similarity_score'], reverse=True)
        
        return {
            'similarity_matrix': similarity_matrix.tolist(),
            'most_similar_pairs': similar_pairs[:15]  # Top 15 similar pairs
        }
    
    def _calculate_roc_metrics(self, y_true, y_pred_proba) -> Dict:
        """Calculate ROC curves and AUC for multiclass classification"""
        # Binarize the output
        y_true_bin = label_binarize(y_true, classes=range(self.num_classes))
        
        # Calculate ROC curve and AUC for each class
        fpr = {}
        tpr = {}
        roc_auc = {}
        
        for i in range(self.num_classes):
            fpr[i], tpr[i], _ = roc_curve(y_true_bin[:, i], y_pred_proba[:, i])
            roc_auc[i] = auc(fpr[i], tpr[i])
        
        # Calculate macro-average ROC curve and AUC
        all_fpr = np.unique(np.concatenate([fpr[i] for i in range(self.num_classes)]))
        mean_tpr = np.zeros_like(all_fpr)
        
        for i in range(self.num_classes):
            mean_tpr += np.interp(all_fpr, fpr[i], tpr[i])
        
        mean_tpr /= self.num_classes
        macro_auc = auc(all_fpr, mean_tpr)
        
        # Per-class AUC scores
        per_class_auc = {
            self.class_names[i]: float(roc_auc[i]) 
            for i in range(self.num_classes)
        }
        
        return {
            'macro_auc': float(macro_auc),
            'per_class_auc': per_class_auc,
            'mean_auc': float(np.mean(list(roc_auc.values())))
        }
    
    def _plot_confusion_matrix(self, cm, cm_normalized):
        """Generate and save confusion matrix plots"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # Raw confusion matrix
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=self.class_names, yticklabels=self.class_names, ax=ax1)
        ax1.set_title('Confusion Matrix (Raw Counts)')
        ax1.set_xlabel('Predicted')
        ax1.set_ylabel('True')
        
        # Normalized confusion matrix
        sns.heatmap(cm_normalized, annot=True, fmt='.2f', cmap='Blues',
                   xticklabels=self.class_names, yticklabels=self.class_names, ax=ax2)
        ax2.set_title('Confusion Matrix (Normalized)')
        ax2.set_xlabel('Predicted')
        ax2.set_ylabel('True')
        
        plt.tight_layout()
        plt.savefig(f'evaluation_results/{self.model_name}_confusion_matrix.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def _save_evaluation_results(self, results: Dict):
        """Save evaluation results to JSON file"""
        os.makedirs('evaluation_results', exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'evaluation_results/{self.model_name}_evaluation_{timestamp}.json'
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"📊 Evaluation results saved to: {filename}")
    
    def _generate_evaluation_report(self, results: Dict):
        """Generate human-readable evaluation report"""
        report_lines = [
            f"🌿 Medicinal Plant Recognition - Evaluation Report",
            f"Model: {self.model_name}",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "=" * 60,
            "",
            "📊 OVERALL PERFORMANCE:",
            f"  Accuracy: {results['basic_metrics']['accuracy']:.3f}",
            f"  Top-3 Accuracy: {results['basic_metrics']['top_3_accuracy']:.3f}",
            f"  Top-5 Accuracy: {results['basic_metrics']['top_5_accuracy']:.3f}",
            f"  Macro F1-Score: {results['basic_metrics']['macro_f1']:.3f}",
            f"  Mean Confidence: {results['confidence_analysis']['overall_mean_confidence']:.3f}",
            f"  Recommended Threshold: {results['confidence_analysis']['recommended_threshold']:.3f}",
            "",
            "🔍 CONFIDENCE ANALYSIS:",
        ]
        
        # Add threshold analysis
        for threshold, metrics in results['confidence_analysis']['threshold_analysis'].items():
            report_lines.append(
                f"  Threshold {threshold}: Accuracy={metrics['accuracy']:.3f}, "
                f"Coverage={metrics['coverage']:.3f}"
            )
        
        report_lines.extend([
            "",
            "⚠️ MOST CONFUSED PLANT PAIRS:",
        ])
        
        # Add most confused pairs
        for pair in results['confusion_matrix']['most_confused_pairs'][:10]:
            report_lines.append(
                f"  {pair['true_class']} → {pair['predicted_class']} "
                f"({pair['confusion_rate']:.3f} rate, {pair['count']} cases)"
            )
        
        report_lines.extend([
            "",
            "🚨 PROBLEMATIC CLASSES:",
        ])
        
        # Add problematic classes
        for cls in results['per_class_metrics']['problematic_classes'][:10]:
            report_lines.append(
                f"  {cls['class']}: F1={cls['f1_score']:.3f}, "
                f"Precision={cls['precision']:.3f}, Recall={cls['recall']:.3f}"
            )
        
        # Save report
        report_filename = f'evaluation_results/{self.model_name}_report.txt'
        with open(report_filename, 'w') as f:
            f.write('\n'.join(report_lines))
        
        print(f"📋 Evaluation report saved to: {report_filename}")
        
        # Print summary to console
        print("\n" + "\n".join(report_lines[:20]))

# Example usage
if __name__ == "__main__":
    # Example class names for medicinal plants
    class_names = [
        'Aloe_vera', 'Andrographis_paniculata', 'Plectranthus_amboinicus',
        'Ocimum_sanctum', 'Curcuma_longa', 'Azadirachta_indica'
        # ... add all your plant classes
    ]
    
    evaluator = PlantEvaluationMetrics(class_names, "EfficientNet_B3")
    print("🔬 Evaluation system initialized successfully!")
    
    # To use with actual model:
    # results = evaluator.evaluate_model(model, test_generator)
