#!/usr/bin/env python3
"""
Test script for the Medicinal Plant Recognition API
"""
import requests
import json
from PIL import Image
import io

def create_test_image():
    """Create a simple test image"""
    # Create a simple 224x224 RGB image
    img = Image.new('RGB', (224, 224), color='green')
    
    # Save to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def test_api():
    """Test the API endpoints"""
    base_url = "http://localhost:5000"
    
    print("Testing Medicinal Plant Recognition API...")
    
    # Test 1: Check if API is running
    print("\n1. Testing API status...")
    try:
        response = requests.get(f"{base_url}/")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
        return
    
    # Test 2: Test image prediction
    print("\n2. Testing image prediction...")
    try:
        test_image = create_test_image()
        files = {'image': ('test.jpg', test_image, 'image/jpeg')}
        
        response = requests.post(f"{base_url}/api/predict", files=files)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("Prediction successful!")
            print(f"Scientific Name: {result.get('scientificName')}")
            print(f"Local Name: {result.get('localName')}")
            print(f"Confidence: {result.get('confidence')}")
            print(f"Medicinal Features: {result.get('medicinalFeature')}")
            print(f"Mock Response: {result.get('_mock', False)}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Test save endpoint
    print("\n3. Testing save endpoint...")
    try:
        test_data = {
            "class_name": "test_plant",
            "confidence": 0.85,
            "scientificName": "Testus plantus",
            "localName": "Test Plant",
            "medicinalFeature": ["test feature"]
        }
        
        response = requests.post(f"{base_url}/api/save", 
                               headers={'Content-Type': 'application/json'},
                               data=json.dumps(test_data))
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 4: Test feedback endpoint
    print("\n4. Testing feedback endpoint...")
    try:
        feedback_data = {
            "fileName": "test.jpg",
            "feedback": "This is a test feedback",
            "result": {"test": "data"}
        }
        
        response = requests.post(f"{base_url}/api/feedback", 
                               headers={'Content-Type': 'application/json'},
                               data=json.dumps(feedback_data))
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    print("\nAPI testing completed!")

if __name__ == "__main__":
    test_api()
