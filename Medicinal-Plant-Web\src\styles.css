:root{--bg:#f7fafc;--card:#fff;--muted:#6b7280;--accent:#0ea5a4}
body { font-family: Inter, system-ui, Arial, sans-serif; padding: 2rem; background:var(--bg); color:#111827 }
.app { max-width: 1100px; margin: 0 auto; background:transparent }
.header { margin-bottom:1rem }
.header h1 { margin:0 }
.muted { color:var(--muted) }

.main-grid { display:grid; grid-template-columns: 360px 1fr; gap:1rem }
.panel { background:var(--card); padding:1rem; border-radius:8px; box-shadow:0 6px 20px rgba(2,6,23,0.06) }

/* Upload options styling */
.upload-options {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.file-input {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f9f9f9;
}

.file-input:hover {
  border-color: var(--accent);
  background: #f0f8ff;
}

.file-input input[type=file] {
  display: none;
}

.camera-btn {
  flex: 1;
  padding: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  transition: transform 0.2s ease;
}

.camera-btn:hover {
  transform: translateY(-2px);
}

/* Camera interface styling */
.camera-interface {
  margin: 16px 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.camera-video {
  width: 100%;
  height: 300px;
  object-fit: cover;
  background: #000;
}

.camera-controls {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #333;
  justify-content: center;
}

.capture-btn {
  padding: 10px 20px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
}

.cancel-btn {
  padding: 10px 20px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
}
.preview { margin-top:0.75rem; display:flex; flex-direction:column; gap:0.5rem; align-items:flex-start }
.preview img { max-width:100%; border-radius:6px; border:1px solid #e6eef0 }
.file-meta { font-size:0.9rem; color:var(--muted) }

.actions { display:flex; gap:0.5rem; margin-top:0.75rem }
.primary { background:var(--accent); color:white; border:none; padding:0.5rem 0.9rem; border-radius:6px }
.ghost { background:transparent; border:1px solid #e5e7eb; padding:0.45rem 0.85rem; border-radius:6px }

.history { margin-top:1rem }
.history ul { list-style:none; padding:0; margin:0; display:flex; flex-direction:column; gap:0.5rem }
.history li { border:1px solid #f1f5f9; padding:0.5rem; border-radius:6px }

.result-panel .placeholder { color:var(--muted) }
.result-card { display:flex; gap:1rem }
.result-left .thumb { width:160px; height:160px; object-fit:cover; border-radius:8px }
.result-right { flex:1 }
.label { margin:0 }
.confidence { display:flex; align-items:center; gap:0.75rem; margin-top:0.5rem }
.bar { background:#f1f5f9; height:10px; flex:1; border-radius:999px; overflow:hidden }
.fill { background:linear-gradient(90deg,var(--accent),#06b6d4); height:100% }
.percent { width:48px; text-align:right; color:var(--muted) }
.description { margin-top:0.5rem }
.raw { margin-top:0.75rem; background:#f8fafc; padding:0.6rem; border-radius:6px; font-size:0.85rem }

.error { color: #9b2c2c; background:#fff5f5; padding:0.5rem; border-radius:4px }

/* Presentation card (screenshot-like) */
.present-card { background:#dfffe6; border-radius:8px; padding:22px; border:8px solid #e0e0e0; box-shadow:0 4px 0 rgba(0,0,0,0.02); }
.present-inner { display:flex; align-items:center; justify-content:space-between; gap:20px }
.present-left { flex:1; text-align:left }
.present-title { text-align:center; font-size:36px; margin:0 0 12px 0 }
.present-body { max-width:640px; margin:0 auto }
.medicinal-list { margin:6px 0 0 22px }
.present-right { width:180px; display:flex; align-items:center; justify-content:center }
.present-image { max-width:140px; max-height:140px; border-radius:4px }

.feedback-btn { background:#1f7a45; color:white; border:none; padding:12px 20px; border-radius:8px; font-size:18px }

/* Enhanced medicinal features styling */
.medicinal-list li.high-usage {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  padding: 8px 12px;
  margin: 4px 0;
  border-radius: 6px;
  border-left: 4px solid #f39c12;
}

.usage-badge {
  display: inline-block;
  margin-left: 8px;
  padding: 2px 6px;
  background: #e74c3c;
  color: white;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
}

.feature-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}

/* Real name and common names styling */
.present-body > div {
  margin: 4px 0;
}

.present-body > div:first-child {
  margin-top: 0;
}

/* Most used medicine highlight box */
.most-used-medicine {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
  border: 2px solid #4CAF50;
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

/* Popular uses badges */
.popular-uses {
  margin-top: 12px;
}

.popular-uses .badge {
  display: inline-block;
  margin: 2px 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.badge.very-high {
  background: linear-gradient(135deg, #FF6B6B, #ee5a52);
}

.badge.high {
  background: linear-gradient(135deg, #4ECDC4, #45b7aa);
}

/* Enhanced title styling */
.present-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  font-weight: bold;
}

/* Comprehensive Result Styling */
.comprehensive-result {
  max-width: 1200px;
  margin: 0 auto;
}

.result-header-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  gap: 24px;
  align-items: center;
}

.plant-image-section {
  flex-shrink: 0;
}

.plant-image {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 12px;
  border: 3px solid rgba(255,255,255,0.3);
}

.plant-info-section {
  flex: 1;
}

.plant-title {
  font-size: 2.5rem;
  margin: 0 0 16px 0;
  font-weight: bold;
}

.mock-badge {
  font-size: 0.8rem;
  background: rgba(255,255,255,0.2);
  padding: 4px 8px;
  border-radius: 12px;
  margin-left: 12px;
}

.plant-names {
  margin-bottom: 16px;
}

.plant-names > div {
  margin: 4px 0;
  font-size: 1.1rem;
}

.primary-medicine {
  background: rgba(255,255,255,0.15);
  padding: 12px;
  border-radius: 8px;
  margin: 16px 0;
  font-size: 1.2rem;
}

.confidence-display {
  margin-top: 16px;
}

.confidence-bar {
  width: 100%;
  height: 8px;
  background: rgba(255,255,255,0.3);
  border-radius: 4px;
  margin-top: 8px;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  transition: width 0.3s ease;
}

/* Tabbed Content */
.tabbed-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
}

.tab-navigation {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-btn {
  flex: 1;
  padding: 16px 12px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: #e9ecef;
}

.tab-btn.active {
  background: white;
  border-bottom-color: #667eea;
  color: #667eea;
}

.tab-content {
  display: none;
  padding: 24px;
  min-height: 400px;
}

.tab-content.active {
  display: block;
}

/* Description Section */
.description-section {
  margin-bottom: 32px;
}

.description-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 16px;
}

.desc-item {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.desc-item strong {
  color: #667eea;
  display: block;
  margin-bottom: 8px;
}

.parts-used, .compounds {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.part-badge, .compound-badge {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.compound-badge {
  background: #28a745;
}

/* Medicinal Features */
.medicinal-features-section h3 {
  margin-bottom: 20px;
  color: #333;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.feature-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #ccc;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
}

.feature-card.very_high {
  border-left-color: #dc3545;
  background: linear-gradient(135deg, #fff5f5, #ffe6e6);
}

.feature-card.high {
  border-left-color: #fd7e14;
  background: linear-gradient(135deg, #fff8f0, #ffebcc);
}

.feature-card.medium {
  border-left-color: #ffc107;
  background: linear-gradient(135deg, #fffbf0, #fff3cd);
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.frequency-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
}

.frequency-badge.very_high {
  background: #dc3545;
  color: white;
}

.frequency-badge.high {
  background: #fd7e14;
  color: white;
}

.frequency-badge.medium {
  background: #ffc107;
  color: #333;
}

.frequency-badge.low {
  background: #6c757d;
  color: white;
}

/* Traditional Systems */
.systems-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.system-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #dee2e6;
}

.system-title {
  color: #495057;
  margin-bottom: 12px;
  font-size: 1.2rem;
}

.system-card ul {
  margin: 8px 0 0 20px;
}

/* Preparation Methods */
.methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.method-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #dee2e6;
}

.method-card h4 {
  color: #495057;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #667eea;
}

.method-details > div {
  margin: 8px 0;
}

/* Safety Information */
.safety-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.safety-card {
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid;
}

.safety-card.warning {
  background: #fff3cd;
  border-left-color: #ffc107;
}

.safety-card.danger {
  background: #f8d7da;
  border-left-color: #dc3545;
}

.safety-card.caution {
  background: #d1ecf1;
  border-left-color: #17a2b8;
}

.safety-card.info {
  background: #d4edda;
  border-left-color: #28a745;
}

.safety-card h4 {
  margin-bottom: 12px;
}

.safety-card ul {
  margin: 8px 0 0 20px;
}

/* Geography */
.geo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.geo-card {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #2196f3;
}

.geo-card h4 {
  color: #1976d2;
  margin-bottom: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-grid {
    grid-template-columns: 1fr;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .plant-title {
    font-size: 2rem;
  }

  .description-grid {
    grid-template-columns: 1fr;
  }

  .tab-navigation {
    flex-wrap: wrap;
  }

  .tab-btn {
    flex: 1 1 50%;
    min-width: 120px;
  }

  .features-grid,
  .systems-grid,
  .methods-grid,
  .safety-grid,
  .geo-grid {
    grid-template-columns: 1fr;
  }
}

/* Enhanced Mode Toggle */
.mode-toggle {
  margin-top: 1rem;
}

.enhanced-mode-btn {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.enhanced-mode-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}