:root{--bg:#f7fafc;--card:#fff;--muted:#6b7280;--accent:#0ea5a4}
body { font-family: Inter, system-ui, Arial, sans-serif; padding: 2rem; background:var(--bg); color:#111827 }
.app { max-width: 1100px; margin: 0 auto; background:transparent }
.header { margin-bottom:1rem }
.header h1 { margin:0 }
.muted { color:var(--muted) }

.main-grid { display:grid; grid-template-columns: 360px 1fr; gap:1rem }
.panel { background:var(--card); padding:1rem; border-radius:8px; box-shadow:0 6px 20px rgba(2,6,23,0.06) }

.file-input input[type=file] { display:block }
.preview { margin-top:0.75rem; display:flex; flex-direction:column; gap:0.5rem; align-items:flex-start }
.preview img { max-width:100%; border-radius:6px; border:1px solid #e6eef0 }
.file-meta { font-size:0.9rem; color:var(--muted) }

.actions { display:flex; gap:0.5rem; margin-top:0.75rem }
.primary { background:var(--accent); color:white; border:none; padding:0.5rem 0.9rem; border-radius:6px }
.ghost { background:transparent; border:1px solid #e5e7eb; padding:0.45rem 0.85rem; border-radius:6px }

.history { margin-top:1rem }
.history ul { list-style:none; padding:0; margin:0; display:flex; flex-direction:column; gap:0.5rem }
.history li { border:1px solid #f1f5f9; padding:0.5rem; border-radius:6px }

.result-panel .placeholder { color:var(--muted) }
.result-card { display:flex; gap:1rem }
.result-left .thumb { width:160px; height:160px; object-fit:cover; border-radius:8px }
.result-right { flex:1 }
.label { margin:0 }
.confidence { display:flex; align-items:center; gap:0.75rem; margin-top:0.5rem }
.bar { background:#f1f5f9; height:10px; flex:1; border-radius:999px; overflow:hidden }
.fill { background:linear-gradient(90deg,var(--accent),#06b6d4); height:100% }
.percent { width:48px; text-align:right; color:var(--muted) }
.description { margin-top:0.5rem }
.raw { margin-top:0.75rem; background:#f8fafc; padding:0.6rem; border-radius:6px; font-size:0.85rem }

.error { color: #9b2c2c; background:#fff5f5; padding:0.5rem; border-radius:4px }

/* Presentation card (screenshot-like) */
.present-card { background:#dfffe6; border-radius:8px; padding:22px; border:8px solid #e0e0e0; box-shadow:0 4px 0 rgba(0,0,0,0.02); }
.present-inner { display:flex; align-items:center; justify-content:space-between; gap:20px }
.present-left { flex:1; text-align:left }
.present-title { text-align:center; font-size:36px; margin:0 0 12px 0 }
.present-body { max-width:640px; margin:0 auto }
.medicinal-list { margin:6px 0 0 22px }
.present-right { width:180px; display:flex; align-items:center; justify-content:center }
.present-image { max-width:140px; max-height:140px; border-radius:4px }

.feedback-btn { background:#1f7a45; color:white; border:none; padding:12px 20px; border-radius:8px; font-size:18px }

/* Enhanced medicinal features styling */
.medicinal-list li.high-usage {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  padding: 8px 12px;
  margin: 4px 0;
  border-radius: 6px;
  border-left: 4px solid #f39c12;
}

.usage-badge {
  display: inline-block;
  margin-left: 8px;
  padding: 2px 6px;
  background: #e74c3c;
  color: white;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
}

.feature-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}

/* Real name and common names styling */
.present-body > div {
  margin: 4px 0;
}

.present-body > div:first-child {
  margin-top: 0;
}

/* Most used medicine highlight box */
.most-used-medicine {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
  border: 2px solid #4CAF50;
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

/* Popular uses badges */
.popular-uses {
  margin-top: 12px;
}

.popular-uses .badge {
  display: inline-block;
  margin: 2px 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.badge.very-high {
  background: linear-gradient(135deg, #FF6B6B, #ee5a52);
}

.badge.high {
  background: linear-gradient(135deg, #4ECDC4, #45b7aa);
}

/* Enhanced title styling */
.present-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-grid {
    grid-template-columns: 1fr;
  }

  .present-inner {
    flex-direction: column;
    text-align: center;
  }

  .present-right {
    width: 100%;
  }
}


