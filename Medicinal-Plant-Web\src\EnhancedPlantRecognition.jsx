import React, { useState, useRef } from 'react';
import './App.css';

const EnhancedPlantRecognition = () => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [prediction, setPrediction] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [confidenceThreshold, setConfidenceThreshold] = useState(70);
  const [useEnhancedSystem, setUseEnhancedSystem] = useState(true);
  const fileInputRef = useRef(null);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const [cameraActive, setCameraActive] = useState(false);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
      setPrediction(null);
      setError(null);
    }
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } 
      });
      videoRef.current.srcObject = stream;
      setCameraActive(true);
      setError(null);
    } catch (err) {
      setError('Camera access denied or not available');
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setCameraActive(false);
  };

  const capturePhoto = () => {
    const canvas = canvasRef.current;
    const video = videoRef.current;
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);
    
    canvas.toBlob((blob) => {
      const file = new File([blob], 'captured-plant.jpg', { type: 'image/jpeg' });
      setSelectedFile(file);
      stopCamera();
    }, 'image/jpeg', 0.8);
  };

  const handlePredict = async () => {
    if (!selectedFile) {
      setError('Please select an image first');
      return;
    }

    setLoading(true);
    setError(null);
    setPrediction(null);

    const formData = new FormData();
    formData.append('image', selectedFile);
    formData.append('confidence_threshold', confidenceThreshold);

    try {
      const endpoint = useEnhancedSystem ? '/api/predict_enhanced' : '/api/predict';
      const response = await fetch(`http://localhost:5000${endpoint}`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setPrediction(result);
    } catch (err) {
      setError(`Prediction failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const renderEnhancedResult = (result) => {
    if (result.status === 'low_confidence') {
      return (
        <div className="prediction-result low-confidence">
          <div className="result-header">
            <h2>❓ {result.message}</h2>
            <p className="confidence-info">Confidence threshold: {result.confidence_threshold}%</p>
          </div>
          
          <div className="top-matches">
            <h3>Top 3 Possible Matches:</h3>
            {result.top_matches.map((match, index) => (
              <div key={index} className="match-item">
                <div className="match-header">
                  <h4>{match.rank}. {match.common_name}</h4>
                  <span className="confidence-badge">{match.confidence}</span>
                </div>
                <p className="scientific-name">{match.scientific_name}</p>
                <div className="key-uses">
                  <strong>Key Uses:</strong> {match.key_uses.join(', ')}
                </div>
                <div className="features">
                  <strong>Features:</strong>
                  <ul>
                    {Object.entries(match.distinguishing_features).map(([key, value]) => (
                      <li key={key}><strong>{key}:</strong> {value}</li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
          
          <div className="recommendation">
            <h3>💡 Recommendation</h3>
            <p>{result.recommendation.advice}</p>
            <div className="tips">
              <strong>Tips for better identification:</strong>
              <ul>
                {result.recommendation.tips.map((tip, index) => (
                  <li key={index}>{tip}</li>
                ))}
              </ul>
            </div>
          </div>
          
          {result.formatted_display && (
            <div className="formatted-display">
              <h3>📋 Detailed Analysis</h3>
              <pre>{result.formatted_display}</pre>
            </div>
          )}
        </div>
      );
    }

    if (result.status === 'unknown') {
      return (
        <div className="prediction-result unknown">
          <div className="result-header">
            <h2>❓ {result.message}</h2>
          </div>
          <div className="recommendation">
            <p>{result.recommendation.advice}</p>
            <p>{result.recommendation.suggestion}</p>
          </div>
        </div>
      );
    }

    if (result.status === 'success') {
      const { identification, overview, traditional_use, preparation, safety, geography } = result;
      
      return (
        <div className="prediction-result success">
          <div className="result-header">
            <h1>{identification.common_name} {identification.emoji}</h1>
            <div className="identification-info">
              <p className="scientific-name"><strong>Scientific Name:</strong> {identification.scientific_name}</p>
              <p className="family"><strong>Family:</strong> {identification.family}</p>
              <div className="confidence-display">
                <span className="confidence-badge high">{identification.confidence}</span>
              </div>
            </div>
            
            <div className="local-names">
              <strong>Local Names:</strong>
              <div className="names-grid">
                {Object.entries(identification.local_names).map(([lang, name]) => (
                  <span key={lang} className="name-tag">
                    {lang.charAt(0).toUpperCase() + lang.slice(1)}: {name}
                  </span>
                ))}
              </div>
            </div>
          </div>

          <div className="content-tabs">
            <div className="tab-content">
              <div className="section overview-section">
                <h2>📋 Overview</h2>
                <p>{overview.description}</p>
                <div className="modern-benefits">
                  <h3>Modern Benefits:</h3>
                  <div className="benefits-grid">
                    {overview.modern_benefits.map((benefit, index) => (
                      <span key={index} className="benefit-tag">{benefit}</span>
                    ))}
                  </div>
                </div>
              </div>

              <div className="section traditional-section">
                <h2>🌿 Traditional Use</h2>
                
                <div className="traditional-systems">
                  <div className="system-card">
                    <h3>Ayurveda</h3>
                    <p><strong>Uses:</strong> {traditional_use.ayurveda.uses.join(', ')}</p>
                    <p><strong>Properties:</strong> {traditional_use.ayurveda.properties}</p>
                  </div>
                  
                  <div className="system-card">
                    <h3>Siddha</h3>
                    <p><strong>Uses:</strong> {traditional_use.siddha.uses.join(', ')}</p>
                    <p><strong>Properties:</strong> {traditional_use.siddha.properties}</p>
                  </div>
                  
                  <div className="system-card">
                    <h3>Unani</h3>
                    <p><strong>Uses:</strong> {traditional_use.unani.uses.join(', ')}</p>
                    <p><strong>Properties:</strong> {traditional_use.unani.properties}</p>
                  </div>
                </div>
              </div>

              <div className="section preparation-section">
                <h2>🧪 Preparation Methods</h2>
                <div className="preparation-methods">
                  {preparation.methods.map((method, index) => (
                    <div key={index} className="method-card">
                      <h4>{method.method}</h4>
                      <p>{method.preparation}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div className="section safety-section">
                <h2>⚠️ Safety Information</h2>
                <div className="safety-warnings">
                  {safety.caution_notes.map((note, index) => (
                    <div key={index} className="warning-item">
                      <span className="warning-icon">⚠️</span>
                      <span>{note}</span>
                    </div>
                  ))}
                  <div className="general-advice">
                    <strong>General Advice:</strong> {safety.general_advice}
                  </div>
                </div>
              </div>

              <div className="section geography-section">
                <h2>🌍 Geography & Habitat</h2>
                <div className="geography-info">
                  <p><strong>Native Region:</strong> {geography.native_region}</p>
                  <p><strong>Habitat:</strong> {geography.habitat}</p>
                  <p><strong>Cultivation:</strong> {geography.cultivation}</p>
                </div>
              </div>
            </div>
          </div>

          {result.formatted_display && (
            <div className="formatted-display">
              <h3>📋 Complete Analysis</h3>
              <pre>{result.formatted_display}</pre>
            </div>
          )}
        </div>
      );
    }

    return null;
  };

  const renderBasicResult = (result) => {
    return (
      <div className="prediction-result basic">
        <div className="result-header">
          <h1>{result.realName}</h1>
          <div className="identification-info">
            <p className="scientific-name"><strong>Scientific Name:</strong> {result.scientificName}</p>
            <p className="local-name"><strong>Local Name:</strong> {result.localName}</p>
            <div className="confidence-display">
              <span className={`confidence-badge ${result.confidence > 0.8 ? 'high' : result.confidence > 0.6 ? 'medium' : 'low'}`}>
                {(result.confidence * 100).toFixed(1)}%
              </span>
            </div>
          </div>
        </div>

        <div className="basic-content">
          <div className="medicinal-features">
            <h3>Medicinal Uses:</h3>
            <div className="features-list">
              {result.medicinalFeature.map((feature, index) => (
                <span key={index} className="feature-tag">{feature}</span>
              ))}
            </div>
          </div>

          <div className="primary-medicine">
            <h3>Primary Medicine:</h3>
            <p>{result.primaryMedicine}</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="enhanced-plant-recognition">
      <div className="header">
        <h1>🌿 Enhanced Medicinal Plant Recognition System</h1>
        <p>Upload or capture a photo of a medicinal plant for detailed identification and traditional medicine information</p>
      </div>

      <div className="controls">
        <div className="system-toggle">
          <label>
            <input
              type="checkbox"
              checked={useEnhancedSystem}
              onChange={(e) => setUseEnhancedSystem(e.target.checked)}
            />
            Use Enhanced Recognition System
          </label>
        </div>

        {useEnhancedSystem && (
          <div className="confidence-control">
            <label>
              Confidence Threshold: {confidenceThreshold}%
              <input
                type="range"
                min="50"
                max="95"
                value={confidenceThreshold}
                onChange={(e) => setConfidenceThreshold(parseInt(e.target.value))}
              />
            </label>
          </div>
        )}
      </div>

      <div className="upload-section">
        <div className="file-upload">
          <input
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            ref={fileInputRef}
            style={{ display: 'none' }}
          />
          <button onClick={() => fileInputRef.current.click()}>
            📁 Select Image
          </button>
        </div>

        <div className="camera-section">
          {!cameraActive ? (
            <button onClick={startCamera}>📷 Use Camera</button>
          ) : (
            <div className="camera-controls">
              <button onClick={capturePhoto}>📸 Capture</button>
              <button onClick={stopCamera}>❌ Stop Camera</button>
            </div>
          )}
        </div>

        {selectedFile && (
          <div className="selected-file">
            <p>Selected: {selectedFile.name}</p>
            <button onClick={handlePredict} disabled={loading}>
              {loading ? '🔄 Analyzing...' : '🔍 Identify Plant'}
            </button>
          </div>
        )}
      </div>

      <div className="camera-preview">
        <video ref={videoRef} autoPlay playsInline style={{ display: cameraActive ? 'block' : 'none' }} />
        <canvas ref={canvasRef} style={{ display: 'none' }} />
      </div>

      {error && (
        <div className="error-message">
          <p>❌ {error}</p>
        </div>
      )}

      {prediction && (
        <div className="results-section">
          {useEnhancedSystem ? renderEnhancedResult(prediction) : renderBasicResult(prediction)}
        </div>
      )}
    </div>
  );
};

export default EnhancedPlantRecognition;
