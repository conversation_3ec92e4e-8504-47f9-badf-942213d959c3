from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os
from datetime import datetime
import random
import sys
import numpy as np

# Add parent directory to path to import our enhanced modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from medicinal_plant_recognition_system import MedicinalPlantRecognitionSystem
    ENHANCED_SYSTEM_AVAILABLE = True
    print("✅ Enhanced medicinal plant recognition system loaded")
except ImportError as e:
    print(f"⚠️ Enhanced system not available: {e}")
    ENHANCED_SYSTEM_AVAILABLE = False

# Load environment variables
MODEL_PATH = os.getenv("MODEL_PATH", "models/plant_model.h5")
CLASSES_PATH = os.getenv("CLASSES_PATH", "models/classes.json")

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Global variables for model and classes
model = None
class_names = {}
enhanced_system = None

# Initialize enhanced system if available
if ENHANCED_SYSTEM_AVAILABLE:
    try:
        enhanced_system = MedicinalPlantRecognitionSystem()
        print("🌿 Enhanced medicinal plant recognition system initialized")
    except Exception as e:
        print(f"⚠️ Failed to initialize enhanced system: {e}")
        enhanced_system = None

# Load class details
try:
    with open(CLASSES_PATH, "r") as f:
        class_names = json.load(f)
    print(f"Loaded {len(class_names)} plant classes")
except Exception as e:
    print(f"Error loading classes: {e}")

# Since TensorFlow is not available, we'll use a mock prediction system
print("Using mock prediction system (TensorFlow not available)")
model = None

def preprocess_image(image_bytes):
    """Simple image validation - in a real app you'd process the image for ML"""
    # For now, just validate that it's a valid image
    try:
        from PIL import Image
        import io
        image = Image.open(io.BytesIO(image_bytes)).convert("RGB")
        return True  # Image is valid
    except Exception:
        return False  # Invalid image

@app.route("/")
def home():
    return jsonify({"message": "Medicinal Plant Recognition API is running 🚀"})

@app.route("/api/predict", methods=["POST"])
def predict():
    if "image" not in request.files:
        return jsonify({"error": "No image file uploaded"}), 400

    file = request.files["image"]
    if file.filename == '':
        return jsonify({"error": "No file selected"}), 400

    try:
        img_bytes = file.read()
        is_valid_image = preprocess_image(img_bytes)

        if not is_valid_image:
            return jsonify({"error": "Invalid image file"}), 400

        # Use mock prediction system (since TensorFlow is not available)
        # In a real implementation, you would use your trained model here

        # For demonstration, prefer plants with comprehensive data (95% chance)
        comprehensive_plants = [9, 13, 30]  # Aloe Vera, Andrographis, Turmeric
        if random.random() < 0.95:
            class_index = random.choice(comprehensive_plants)
        else:
            class_index = random.randint(0, len(class_names) - 1)

        confidence = round(random.uniform(0.6, 0.95), 3)

        plant_data = class_names[str(class_index)]

        # Extract feature names and usage info
        features_list = []
        most_used_features = []

        for feature in plant_data["features"]:
            features_list.append(feature["name"])
            if feature["usage_frequency"] in ["very_high", "high"]:
                most_used_features.append({
                    "name": feature["name"],
                    "usage": feature["usage_frequency"],
                    "description": feature["description"]
                })

        result = {
            "scientificName": plant_data["scientific_name"],
            "localName": plant_data["local_name"],
            "realName": plant_data["real_name"],
            "commonNames": plant_data["common_names"],
            "medicinalFeature": features_list,
            "medicinalDetails": plant_data["features"],
            "mostUsedMedicines": most_used_features,
            "primaryMedicine": plant_data["most_used_medicine"],

            # Enhanced comprehensive data
            "description": plant_data.get("description", {}),
            "traditionalSystems": plant_data.get("traditional_systems", {}),
            "preparationMethods": plant_data.get("preparation_methods", []),
            "safetyInfo": plant_data.get("safety_info", {}),
            "geographicalDistribution": plant_data.get("geographical_distribution", {}),

            "confidence": confidence,
            "label": plant_data["scientific_name"].lower().replace(" ", "_"),
            "_mock": True
        }

        return jsonify(result)

    except Exception as e:
        return jsonify({"error": f"Prediction failed: {str(e)}"}), 500

@app.route("/api/predict_enhanced", methods=["POST"])
def predict_enhanced():
    """Enhanced prediction using the new medicinal plant recognition system"""
    try:
        if "image" not in request.files:
            return jsonify({"error": "No image file uploaded"}), 400

        file = request.files["image"]
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        if not enhanced_system:
            return jsonify({"error": "Enhanced system not available. Using basic system."}), 503

        # Read image file
        img_bytes = file.read()
        is_valid_image = preprocess_image(img_bytes)

        if not is_valid_image:
            return jsonify({"error": "Invalid image file"}), 400

        # Convert to numpy array (mock for now - in real implementation, use PIL/cv2)
        mock_image = np.random.rand(224, 224, 3)

        # Get confidence threshold from request (default 70%)
        confidence_threshold = float(request.form.get('confidence_threshold', 70))

        # Use enhanced system for prediction
        result = enhanced_system.identify_plant(mock_image, confidence_threshold)

        # Format for frontend
        if "top_3_matches" in result:
            # Low confidence result
            response = {
                'status': 'low_confidence',
                'confidence_threshold': confidence_threshold,
                'message': result['identification']['message'],
                'top_matches': result['top_3_matches'],
                'recommendation': result['recommendation'],
                'formatted_display': enhanced_system.display_formatted_result(result),
                'timestamp': datetime.now().isoformat()
            }
        elif result["identification"].get("status") == "Unknown Plant":
            # Unknown plant
            response = {
                'status': 'unknown',
                'message': result['identification']['message'],
                'recommendation': result['recommendation'],
                'formatted_display': enhanced_system.display_formatted_result(result),
                'timestamp': datetime.now().isoformat()
            }
        else:
            # High confidence result
            response = {
                'status': 'success',
                'identification': result['identification'],
                'overview': result['overview'],
                'traditional_use': result['traditional_use'],
                'preparation': result['preparation'],
                'safety': result['safety'],
                'geography': result['geography'],
                'distinguishing_features': result['distinguishing_features'],
                'formatted_display': enhanced_system.display_formatted_result(result),
                'timestamp': datetime.now().isoformat()
            }

        return jsonify(response)

    except Exception as e:
        return jsonify({"error": f"Enhanced prediction failed: {str(e)}"}), 500

@app.route("/api/save", methods=["POST"])
def save_record():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Here you would typically save to a database
        # For now, we'll just log it and return success
        print(f"Saving record: {data}")

        # You can add database logic here later
        # For example: db.save_plant_record(data)

        return jsonify({"message": "Record saved successfully", "timestamp": datetime.now().isoformat()})

    except Exception as e:
        return jsonify({"error": f"Save failed: {str(e)}"}), 500

@app.route("/api/feedback", methods=["POST"])
def save_feedback():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No feedback data provided"}), 400

        # Here you would typically save feedback to a database
        # For now, we'll just log it and return success
        print(f"Feedback received: {data}")

        # You can add database logic here later
        # For example: db.save_feedback(data)

        return jsonify({"message": "Feedback saved successfully", "timestamp": datetime.now().isoformat()})

    except Exception as e:
        return jsonify({"error": f"Feedback save failed: {str(e)}"}), 500

if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=5000)
