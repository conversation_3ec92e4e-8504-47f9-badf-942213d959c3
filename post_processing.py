#!/usr/bin/env python3
"""
🌿 Medicinal Plant Recognition - Advanced Post-Processing Logic
Implements confidence thresholding, uncertainty handling, and intelligent prediction filtering
"""

import numpy as np
import tensorflow as tf
from typing import Dict, List, Tuple, Optional, Union
import json
from dataclasses import dataclass
from enum import Enum

class ConfidenceLevel(Enum):
    """Confidence level categories"""
    VERY_HIGH = "very_high"    # >95%
    HIGH = "high"              # 80-95%
    MEDIUM = "medium"          # 60-80%
    LOW = "low"                # 40-60%
    VERY_LOW = "very_low"      # <40%

@dataclass
class PredictionResult:
    """Structured prediction result with confidence analysis"""
    predicted_class: str
    confidence: float
    confidence_level: ConfidenceLevel
    top_k_predictions: List[Dict]
    uncertainty_score: float
    recommendation: str
    should_show_result: bool
    alternative_suggestions: List[str]
    metadata: Dict

class PlantPostProcessor:
    """Advanced post-processing for medicinal plant recognition"""
    
    def __init__(self, class_names: List[str], confidence_thresholds: Optional[Dict] = None):
        self.class_names = class_names
        self.num_classes = len(class_names)
        
        # Default confidence thresholds
        self.thresholds = confidence_thresholds or {
            'very_high': 0.95,
            'high': 0.80,
            'medium': 0.60,
            'low': 0.40,
            'minimum_display': 0.50,  # Minimum confidence to show result
            'uncertainty_threshold': 0.30  # Max difference between top 2 predictions
        }
        
        # Load plant similarity data for better recommendations
        self.plant_similarities = self._load_plant_similarities()
        
        # Common confusion pairs (plants often misclassified)
        self.confusion_pairs = self._define_confusion_pairs()
    
    def process_prediction(self, predictions: np.ndarray, 
                         image_quality_score: Optional[float] = None) -> PredictionResult:
        """
        Process raw model predictions with comprehensive analysis
        
        Args:
            predictions: Raw model output probabilities
            image_quality_score: Optional image quality assessment (0-1)
        
        Returns:
            PredictionResult with detailed analysis and recommendations
        """
        # Get top predictions
        top_k_indices = np.argsort(predictions)[-5:][::-1]  # Top 5
        top_k_predictions = [
            {
                'class': self.class_names[idx],
                'confidence': float(predictions[idx]),
                'rank': rank + 1
            }
            for rank, idx in enumerate(top_k_indices)
        ]
        
        # Primary prediction
        predicted_idx = top_k_indices[0]
        predicted_class = self.class_names[predicted_idx]
        confidence = float(predictions[predicted_idx])
        
        # Calculate uncertainty metrics
        uncertainty_score = self._calculate_uncertainty(predictions)
        confidence_level = self._determine_confidence_level(confidence)
        
        # Determine if result should be shown
        should_show = self._should_show_result(
            confidence, uncertainty_score, image_quality_score
        )
        
        # Generate recommendations
        recommendation = self._generate_recommendation(
            confidence, uncertainty_score, confidence_level, 
            top_k_predictions, image_quality_score
        )
        
        # Get alternative suggestions
        alternatives = self._get_alternative_suggestions(
            predicted_class, top_k_predictions, uncertainty_score
        )
        
        # Compile metadata
        metadata = {
            'entropy': float(self._calculate_entropy(predictions)),
            'top_2_difference': float(predictions[top_k_indices[0]] - predictions[top_k_indices[1]]),
            'prediction_spread': float(np.std(predictions)),
            'image_quality_score': image_quality_score,
            'confusion_risk': self._assess_confusion_risk(predicted_class, top_k_predictions)
        }
        
        return PredictionResult(
            predicted_class=predicted_class,
            confidence=confidence,
            confidence_level=confidence_level,
            top_k_predictions=top_k_predictions,
            uncertainty_score=uncertainty_score,
            recommendation=recommendation,
            should_show_result=should_show,
            alternative_suggestions=alternatives,
            metadata=metadata
        )
    
    def _calculate_uncertainty(self, predictions: np.ndarray) -> float:
        """Calculate prediction uncertainty using multiple metrics"""
        # Entropy-based uncertainty
        entropy = -np.sum(predictions * np.log(predictions + 1e-10))
        max_entropy = np.log(self.num_classes)
        normalized_entropy = entropy / max_entropy
        
        # Top-2 difference (lower difference = higher uncertainty)
        sorted_preds = np.sort(predictions)[::-1]
        top_2_diff = sorted_preds[0] - sorted_preds[1]
        
        # Combine metrics (higher score = more uncertain)
        uncertainty = 0.7 * normalized_entropy + 0.3 * (1 - top_2_diff)
        
        return float(uncertainty)
    
    def _calculate_entropy(self, predictions: np.ndarray) -> float:
        """Calculate Shannon entropy of predictions"""
        return -np.sum(predictions * np.log(predictions + 1e-10))
    
    def _determine_confidence_level(self, confidence: float) -> ConfidenceLevel:
        """Categorize confidence level"""
        if confidence >= self.thresholds['very_high']:
            return ConfidenceLevel.VERY_HIGH
        elif confidence >= self.thresholds['high']:
            return ConfidenceLevel.HIGH
        elif confidence >= self.thresholds['medium']:
            return ConfidenceLevel.MEDIUM
        elif confidence >= self.thresholds['low']:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW
    
    def _should_show_result(self, confidence: float, uncertainty: float, 
                           image_quality: Optional[float]) -> bool:
        """Determine if prediction should be shown to user"""
        # Basic confidence threshold
        if confidence < self.thresholds['minimum_display']:
            return False
        
        # High uncertainty check
        if uncertainty > 0.7:  # Very uncertain
            return False
        
        # Image quality consideration
        if image_quality is not None and image_quality < 0.3:
            return False
        
        # Additional checks for edge cases
        if confidence < 0.6 and uncertainty > 0.5:
            return False
        
        return True
    
    def _generate_recommendation(self, confidence: float, uncertainty: float,
                               confidence_level: ConfidenceLevel,
                               top_predictions: List[Dict],
                               image_quality: Optional[float]) -> str:
        """Generate user-friendly recommendation"""
        
        if not self._should_show_result(confidence, uncertainty, image_quality):
            reasons = []
            if confidence < self.thresholds['minimum_display']:
                reasons.append(f"low confidence ({confidence:.1%})")
            if uncertainty > 0.7:
                reasons.append("high uncertainty between multiple plants")
            if image_quality is not None and image_quality < 0.3:
                reasons.append("poor image quality")
            
            return (
                f"❓ Unable to identify plant reliably due to {', '.join(reasons)}. "
                f"Please try:\n"
                f"• Taking a clearer, well-lit photo\n"
                f"• Focusing on distinctive leaves or flowers\n"
                f"• Capturing the plant from multiple angles\n"
                f"• Ensuring the plant fills most of the frame"
            )
        
        # Generate confidence-based recommendations
        if confidence_level == ConfidenceLevel.VERY_HIGH:
            return (
                f"✅ Very confident identification! "
                f"This appears to be {top_predictions[0]['class']} with {confidence:.1%} confidence."
            )
        
        elif confidence_level == ConfidenceLevel.HIGH:
            return (
                f"✅ High confidence identification. "
                f"This is likely {top_predictions[0]['class']} ({confidence:.1%} confidence). "
                f"Consider the alternative possibilities below."
            )
        
        elif confidence_level == ConfidenceLevel.MEDIUM:
            second_choice = top_predictions[1]['class']
            return (
                f"⚠️ Moderate confidence. This could be {top_predictions[0]['class']} "
                f"({confidence:.1%}) or possibly {second_choice} "
                f"({top_predictions[1]['confidence']:.1%}). "
                f"Please verify with additional photos or expert consultation."
            )
        
        else:  # LOW or VERY_LOW
            return (
                f"⚠️ Low confidence identification. Multiple plants are possible. "
                f"Please take additional photos focusing on distinctive features "
                f"like leaf shape, arrangement, and any flowers or fruits."
            )
    
    def _get_alternative_suggestions(self, predicted_class: str, 
                                   top_predictions: List[Dict],
                                   uncertainty: float) -> List[str]:
        """Get alternative plant suggestions based on uncertainty and similarity"""
        alternatives = []
        
        # If uncertainty is high, suggest top alternatives
        if uncertainty > 0.4:
            for pred in top_predictions[1:4]:  # Top 2-4 alternatives
                if pred['confidence'] > 0.1:  # Only meaningful alternatives
                    alternatives.append(
                        f"{pred['class']} ({pred['confidence']:.1%} confidence)"
                    )
        
        # Add similar plants from confusion pairs
        if predicted_class in self.confusion_pairs:
            similar_plants = self.confusion_pairs[predicted_class]
            for similar in similar_plants:
                if similar not in [p['class'] for p in top_predictions[:3]]:
                    alternatives.append(f"{similar} (commonly confused)")
        
        return alternatives[:5]  # Limit to 5 alternatives
    
    def _assess_confusion_risk(self, predicted_class: str, 
                             top_predictions: List[Dict]) -> str:
        """Assess risk of confusion with similar plants"""
        if predicted_class in self.confusion_pairs:
            confused_plants = self.confusion_pairs[predicted_class]
            top_classes = [p['class'] for p in top_predictions[:3]]
            
            # Check if any confused plants are in top predictions
            overlapping = set(confused_plants) & set(top_classes)
            if overlapping:
                return f"High - commonly confused with {', '.join(overlapping)}"
        
        # Check top-2 difference
        if len(top_predictions) >= 2:
            diff = top_predictions[0]['confidence'] - top_predictions[1]['confidence']
            if diff < 0.2:  # Small difference
                return f"Medium - close to {top_predictions[1]['class']}"
        
        return "Low"
    
    def _load_plant_similarities(self) -> Dict:
        """Load plant similarity data (could be from file or database)"""
        # This would typically load from a file or database
        # For now, return empty dict
        return {}
    
    def _define_confusion_pairs(self) -> Dict[str, List[str]]:
        """Define commonly confused plant pairs"""
        return {
            'Andrographis_paniculata': ['Plectranthus_amboinicus', 'Ocimum_sanctum'],
            'Plectranthus_amboinicus': ['Andrographis_paniculata', 'Coleus_forskohlii'],
            'Ocimum_sanctum': ['Ocimum_basilicum', 'Andrographis_paniculata'],
            'Ocimum_basilicum': ['Ocimum_sanctum', 'Mentha_spicata'],
            'Mentha_spicata': ['Mentha_piperita', 'Ocimum_basilicum'],
            'Mentha_piperita': ['Mentha_spicata', 'Plectranthus_amboinicus'],
            'Aloe_vera': ['Aloe_arborescens', 'Agave_americana'],
            'Curcuma_longa': ['Curcuma_aromatica', 'Zingiber_officinale'],
            # Add more confusion pairs based on your specific plants
        }

class ImageQualityAssessment:
    """Assess image quality for better post-processing decisions"""
    
    @staticmethod
    def assess_quality(image: np.ndarray) -> float:
        """
        Assess image quality score (0-1)
        
        Args:
            image: Input image as numpy array
            
        Returns:
            Quality score between 0 (poor) and 1 (excellent)
        """
        scores = []
        
        # Sharpness assessment (Laplacian variance)
        gray = tf.image.rgb_to_grayscale(image)
        laplacian_var = tf.image.sobel_edges(gray)
        sharpness = tf.reduce_mean(tf.square(laplacian_var))
        scores.append(min(float(sharpness) / 100, 1.0))  # Normalize
        
        # Brightness assessment
        brightness = tf.reduce_mean(image)
        brightness_score = 1.0 - abs(0.5 - float(brightness))  # Optimal around 0.5
        scores.append(brightness_score)
        
        # Contrast assessment
        contrast = tf.reduce_std(image)
        contrast_score = min(float(contrast) / 0.3, 1.0)  # Normalize
        scores.append(contrast_score)
        
        # Overall quality (weighted average)
        quality = 0.5 * scores[0] + 0.3 * scores[1] + 0.2 * scores[2]
        return float(quality)

class BatchPostProcessor:
    """Process multiple predictions efficiently"""
    
    def __init__(self, post_processor: PlantPostProcessor):
        self.post_processor = post_processor
    
    def process_batch(self, predictions_batch: np.ndarray, 
                     image_qualities: Optional[List[float]] = None) -> List[PredictionResult]:
        """Process a batch of predictions"""
        results = []
        
        for i, predictions in enumerate(predictions_batch):
            quality = image_qualities[i] if image_qualities else None
            result = self.post_processor.process_prediction(predictions, quality)
            results.append(result)
        
        return results
    
    def get_batch_statistics(self, results: List[PredictionResult]) -> Dict:
        """Get statistics for a batch of results"""
        total = len(results)
        shown = sum(1 for r in results if r.should_show_result)
        
        confidence_dist = {}
        for level in ConfidenceLevel:
            count = sum(1 for r in results if r.confidence_level == level)
            confidence_dist[level.value] = count / total
        
        return {
            'total_predictions': total,
            'shown_predictions': shown,
            'rejection_rate': (total - shown) / total,
            'confidence_distribution': confidence_dist,
            'mean_confidence': np.mean([r.confidence for r in results]),
            'mean_uncertainty': np.mean([r.uncertainty_score for r in results])
        }

# Example usage and testing
if __name__ == "__main__":
    # Example class names
    class_names = [
        'Aloe_vera', 'Andrographis_paniculata', 'Plectranthus_amboinicus',
        'Ocimum_sanctum', 'Curcuma_longa', 'Azadirachta_indica'
    ]
    
    # Initialize post-processor
    post_processor = PlantPostProcessor(class_names)
    
    # Example prediction (simulated)
    example_predictions = np.array([0.65, 0.20, 0.08, 0.04, 0.02, 0.01])
    
    # Process prediction
    result = post_processor.process_prediction(example_predictions, image_quality_score=0.8)
    
    print("🌿 Post-Processing Result:")
    print(f"Predicted: {result.predicted_class}")
    print(f"Confidence: {result.confidence:.1%}")
    print(f"Should show: {result.should_show_result}")
    print(f"Recommendation: {result.recommendation}")
    print(f"Alternatives: {result.alternative_suggestions}")
    
    print("\n✅ Post-processing system initialized successfully!")
