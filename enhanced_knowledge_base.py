#!/usr/bin/env python3
"""
🌿 Enhanced Knowledge Base Integration for Medicinal Plant Recognition
Comprehensive database linking with detailed plant information and intelligent search
"""

import json
import sqlite3
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import re
from fuzzywuzzy import fuzz, process

@dataclass
class PlantInfo:
    """Comprehensive plant information structure"""
    scientific_name: str
    local_name: str
    real_name: str
    common_names: List[str]
    description: Dict
    features: List[Dict]
    traditional_systems: Dict
    preparation_methods: List[Dict]
    safety_info: Dict
    geographical_distribution: Dict
    distinguishing_features: Dict
    most_used_medicine: str
    
    # Additional metadata
    confidence_score: Optional[float] = None
    alternative_names: Optional[List[str]] = None
    confusion_risk: Optional[str] = None
    expert_verified: bool = False
    last_updated: Optional[str] = None

class EnhancedKnowledgeBase:
    """Enhanced knowledge base with intelligent search and comprehensive plant data"""
    
    def __init__(self, json_file: str = "models/classes.json", db_file: str = "plant_knowledge.db"):
        self.json_file = json_file
        self.db_file = db_file
        self.plants_data = {}
        self.search_index = {}
        
        # Load and enhance data
        self._load_plant_data()
        self._create_search_index()
        self._initialize_database()
    
    def _load_plant_data(self):
        """Load and enhance plant data from JSON"""
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            # Convert to enhanced format
            for plant_id, plant_data in raw_data.items():
                enhanced_plant = self._enhance_plant_data(plant_data)
                self.plants_data[plant_id] = enhanced_plant
                
        except FileNotFoundError:
            print(f"⚠️ Warning: {self.json_file} not found. Using minimal dataset.")
            self._create_minimal_dataset()
    
    def _enhance_plant_data(self, plant_data: Dict) -> PlantInfo:
        """Enhance basic plant data with additional information"""
        
        # Ensure all required fields exist
        enhanced_data = {
            'scientific_name': plant_data.get('scientific_name', 'Unknown'),
            'local_name': plant_data.get('local_name', 'Unknown'),
            'real_name': plant_data.get('real_name', plant_data.get('local_name', 'Unknown')),
            'common_names': plant_data.get('common_names', []),
            'description': plant_data.get('description', {}),
            'features': plant_data.get('features', []),
            'traditional_systems': plant_data.get('traditional_systems', {}),
            'preparation_methods': plant_data.get('preparation_methods', []),
            'safety_info': plant_data.get('safety_info', {}),
            'geographical_distribution': plant_data.get('geographical_distribution', {}),
            'distinguishing_features': plant_data.get('distinguishing_features', {}),
            'most_used_medicine': plant_data.get('most_used_medicine', 'Unknown'),
            'last_updated': datetime.now().isoformat()
        }
        
        # Add alternative names for better search
        alternative_names = set()
        alternative_names.update(enhanced_data['common_names'])
        
        # Add traditional system names
        for system_data in enhanced_data['traditional_systems'].values():
            if isinstance(system_data, dict) and 'name' in system_data:
                alternative_names.add(system_data['name'])
        
        enhanced_data['alternative_names'] = list(alternative_names)
        
        return PlantInfo(**enhanced_data)
    
    def _create_search_index(self):
        """Create search index for fast plant lookup"""
        self.search_index = {
            'scientific_names': {},
            'common_names': {},
            'alternative_names': {},
            'features': {},
            'traditional_names': {}
        }
        
        for plant_id, plant in self.plants_data.items():
            # Index scientific name
            self.search_index['scientific_names'][plant.scientific_name.lower()] = plant_id
            
            # Index common names
            for name in plant.common_names:
                self.search_index['common_names'][name.lower()] = plant_id
            
            # Index alternative names
            if plant.alternative_names:
                for name in plant.alternative_names:
                    self.search_index['alternative_names'][name.lower()] = plant_id
            
            # Index medicinal features
            for feature in plant.features:
                feature_name = feature.get('name', '').lower()
                if feature_name not in self.search_index['features']:
                    self.search_index['features'][feature_name] = []
                self.search_index['features'][feature_name].append(plant_id)
            
            # Index traditional names
            for system_data in plant.traditional_systems.values():
                if isinstance(system_data, dict) and 'name' in system_data:
                    trad_name = system_data['name'].lower()
                    self.search_index['traditional_names'][trad_name] = plant_id
    
    def _initialize_database(self):
        """Initialize SQLite database for advanced queries"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS plants (
                id TEXT PRIMARY KEY,
                scientific_name TEXT,
                local_name TEXT,
                real_name TEXT,
                most_used_medicine TEXT,
                expert_verified BOOLEAN,
                last_updated TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS plant_names (
                plant_id TEXT,
                name TEXT,
                name_type TEXT,
                FOREIGN KEY (plant_id) REFERENCES plants (id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS plant_features (
                plant_id TEXT,
                feature_name TEXT,
                usage_frequency TEXT,
                description TEXT,
                FOREIGN KEY (plant_id) REFERENCES plants (id)
            )
        ''')
        
        # Populate database
        for plant_id, plant in self.plants_data.items():
            cursor.execute('''
                INSERT OR REPLACE INTO plants 
                (id, scientific_name, local_name, real_name, most_used_medicine, expert_verified, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (plant_id, plant.scientific_name, plant.local_name, plant.real_name,
                  plant.most_used_medicine, plant.expert_verified, plant.last_updated))
            
            # Insert names
            all_names = [plant.scientific_name, plant.local_name, plant.real_name] + plant.common_names
            if plant.alternative_names:
                all_names.extend(plant.alternative_names)
            
            for name in set(all_names):  # Remove duplicates
                cursor.execute('''
                    INSERT OR REPLACE INTO plant_names (plant_id, name, name_type)
                    VALUES (?, ?, ?)
                ''', (plant_id, name, 'common'))
            
            # Insert features
            for feature in plant.features:
                cursor.execute('''
                    INSERT OR REPLACE INTO plant_features 
                    (plant_id, feature_name, usage_frequency, description)
                    VALUES (?, ?, ?, ?)
                ''', (plant_id, feature.get('name'), feature.get('usage_frequency'), 
                      feature.get('description')))
        
        conn.commit()
        conn.close()
    
    def get_plant_by_id(self, plant_id: str) -> Optional[PlantInfo]:
        """Get plant information by ID"""
        return self.plants_data.get(plant_id)
    
    def search_plants(self, query: str, limit: int = 5) -> List[Tuple[str, PlantInfo, float]]:
        """
        Intelligent plant search with fuzzy matching
        
        Returns:
            List of (plant_id, plant_info, similarity_score) tuples
        """
        query_lower = query.lower()
        results = []
        
        # Exact matches first
        for search_type, index in self.search_index.items():
            if query_lower in index:
                plant_id = index[query_lower]
                if isinstance(plant_id, list):
                    for pid in plant_id:
                        results.append((pid, self.plants_data[pid], 1.0))
                else:
                    results.append((plant_id, self.plants_data[plant_id], 1.0))
        
        # Fuzzy matching for partial matches
        all_names = []
        for search_type, index in self.search_index.items():
            if search_type != 'features':  # Features handled separately
                all_names.extend([(name, plant_id) for name, plant_id in index.items()])
        
        # Use fuzzy matching
        fuzzy_matches = process.extract(query_lower, [name for name, _ in all_names], limit=limit*2)
        
        for match, score in fuzzy_matches:
            if score > 70:  # Minimum similarity threshold
                # Find corresponding plant_id
                for name, plant_id in all_names:
                    if name == match:
                        if isinstance(plant_id, list):
                            for pid in plant_id:
                                results.append((pid, self.plants_data[pid], score/100))
                        else:
                            results.append((plant_id, self.plants_data[plant_id], score/100))
                        break
        
        # Remove duplicates and sort by score
        unique_results = {}
        for plant_id, plant_info, score in results:
            if plant_id not in unique_results or unique_results[plant_id][1] < score:
                unique_results[plant_id] = (plant_info, score)
        
        final_results = [(pid, info, score) for pid, (info, score) in unique_results.items()]
        final_results.sort(key=lambda x: x[2], reverse=True)
        
        return final_results[:limit]
    
    def search_by_symptoms(self, symptoms: List[str]) -> List[Tuple[str, PlantInfo, float]]:
        """Search plants by medicinal symptoms/uses"""
        symptom_matches = {}
        
        for symptom in symptoms:
            symptom_lower = symptom.lower()
            
            # Search in features
            for feature_name, plant_ids in self.search_index['features'].items():
                if symptom_lower in feature_name or fuzz.partial_ratio(symptom_lower, feature_name) > 80:
                    for plant_id in plant_ids:
                        if plant_id not in symptom_matches:
                            symptom_matches[plant_id] = 0
                        symptom_matches[plant_id] += 1
        
        # Convert to results format
        results = []
        for plant_id, match_count in symptom_matches.items():
            plant_info = self.plants_data[plant_id]
            # Score based on number of matching symptoms
            score = min(match_count / len(symptoms), 1.0)
            results.append((plant_id, plant_info, score))
        
        results.sort(key=lambda x: x[2], reverse=True)
        return results[:10]
    
    def get_similar_plants(self, plant_id: str, limit: int = 5) -> List[Tuple[str, PlantInfo, str]]:
        """Get plants similar to the given plant"""
        if plant_id not in self.plants_data:
            return []
        
        target_plant = self.plants_data[plant_id]
        similar_plants = []
        
        # Find plants with similar features
        target_features = set(f['name'].lower() for f in target_plant.features)
        
        for pid, plant in self.plants_data.items():
            if pid == plant_id:
                continue
            
            plant_features = set(f['name'].lower() for f in plant.features)
            
            # Calculate feature similarity
            common_features = target_features & plant_features
            total_features = target_features | plant_features
            
            if total_features:
                similarity = len(common_features) / len(total_features)
                if similarity > 0.3:  # Minimum similarity threshold
                    reason = f"Shares {len(common_features)} medicinal uses"
                    similar_plants.append((pid, plant, reason))
        
        # Sort by similarity and return top results
        similar_plants.sort(key=lambda x: len(x[2]), reverse=True)
        return similar_plants[:limit]
    
    def get_confusion_pairs(self, plant_id: str) -> List[Tuple[str, PlantInfo, str]]:
        """Get plants commonly confused with the given plant"""
        if plant_id not in self.plants_data:
            return []
        
        target_plant = self.plants_data[plant_id]
        confusion_pairs = []
        
        # Check for plants with similar names
        target_words = set(re.findall(r'\w+', target_plant.scientific_name.lower()))
        
        for pid, plant in self.plants_data.items():
            if pid == plant_id:
                continue
            
            plant_words = set(re.findall(r'\w+', plant.scientific_name.lower()))
            common_words = target_words & plant_words
            
            if common_words:
                reason = f"Similar scientific name ({', '.join(common_words)})"
                confusion_pairs.append((pid, plant, reason))
        
        return confusion_pairs[:5]
    
    def add_user_feedback(self, plant_id: str, feedback: Dict):
        """Add user feedback to improve knowledge base"""
        # This would typically update a feedback database
        # For now, just log the feedback
        timestamp = datetime.now().isoformat()
        feedback_entry = {
            'plant_id': plant_id,
            'feedback': feedback,
            'timestamp': timestamp
        }
        
        # In a real implementation, this would be stored in a database
        print(f"📝 Feedback recorded for {plant_id}: {feedback}")
    
    def get_plant_statistics(self) -> Dict:
        """Get statistics about the knowledge base"""
        total_plants = len(self.plants_data)
        
        # Count plants with comprehensive data
        comprehensive_count = 0
        for plant in self.plants_data.values():
            if (plant.description and plant.traditional_systems and 
                plant.preparation_methods and plant.safety_info):
                comprehensive_count += 1
        
        # Feature statistics
        all_features = []
        for plant in self.plants_data.values():
            all_features.extend([f['name'] for f in plant.features])
        
        unique_features = len(set(all_features))
        
        return {
            'total_plants': total_plants,
            'comprehensive_plants': comprehensive_count,
            'completion_rate': comprehensive_count / total_plants if total_plants > 0 else 0,
            'unique_medicinal_features': unique_features,
            'average_features_per_plant': len(all_features) / total_plants if total_plants > 0 else 0
        }
    
    def _create_minimal_dataset(self):
        """Create minimal dataset for testing"""
        minimal_plants = {
            "0": {
                "scientific_name": "Plectranthus amboinicus",
                "local_name": "Indian Borage",
                "real_name": "Cuban Oregano",
                "common_names": ["Indian Borage", "Cuban Oregano", "Ajwain Patta", "Karpooravalli"],
                "features": [
                    {"name": "Respiratory ailments", "usage_frequency": "very_high", "description": "Excellent for cough and cold"},
                    {"name": "Digestive aid", "usage_frequency": "high", "description": "Helps with stomach problems"}
                ],
                "most_used_medicine": "Respiratory and digestive treatment"
            },
            "1": {
                "scientific_name": "Andrographis paniculata",
                "local_name": "King of Bitters",
                "real_name": "Green Chiretta",
                "common_names": ["King of Bitters", "Green Chiretta", "Kalmegh"],
                "features": [
                    {"name": "Immune booster", "usage_frequency": "very_high", "description": "Powerful immune enhancer"},
                    {"name": "Fever reducer", "usage_frequency": "very_high", "description": "Effective fever treatment"}
                ],
                "most_used_medicine": "Immune support and fever treatment"
            }
        }
        
        for plant_id, plant_data in minimal_plants.items():
            enhanced_plant = self._enhance_plant_data(plant_data)
            self.plants_data[plant_id] = enhanced_plant

# Example usage and testing
if __name__ == "__main__":
    # Initialize knowledge base
    kb = EnhancedKnowledgeBase()
    
    print("🌿 Enhanced Knowledge Base Initialized")
    print(f"📊 Statistics: {kb.get_plant_statistics()}")
    
    # Test search functionality
    print("\n🔍 Testing search functionality:")
    
    # Search by name
    results = kb.search_plants("Indian Borage")
    for plant_id, plant, score in results:
        print(f"  Found: {plant.scientific_name} ({plant.real_name}) - Score: {score:.2f}")
    
    # Search by symptoms
    symptom_results = kb.search_by_symptoms(["cough", "respiratory"])
    for plant_id, plant, score in symptom_results:
        print(f"  For respiratory issues: {plant.scientific_name} - Score: {score:.2f}")
    
    print("\n✅ Knowledge base integration completed successfully!")
