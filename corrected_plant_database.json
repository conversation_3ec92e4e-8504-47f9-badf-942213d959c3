{"aloe_vera": {"scientific_name": "Aloe vera", "local_name": "<PERSON><PERSON>", "hindi_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tamil_name": "Katralai", "telugu_name": "<PERSON><PERSON><PERSON><PERSON>", "bengali_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marathi_name": "<PERSON><PERSON><PERSON>", "gujarati_name": "<PERSON><PERSON><PERSON>", "common_names": ["<PERSON><PERSON>", "True Aloe", "Medicinal Aloe", "Barbados Aloe"], "medicinal_features": [{"name": "Skin healing", "usage_frequency": "very_high", "description": "Heals cuts, burns, wounds, and skin irritations naturally", "preparation": "Apply fresh gel directly to affected area 2-3 times daily"}, {"name": "Burns treatment", "usage_frequency": "very_high", "description": "Excellent for sunburn, minor burns, and heat rashes", "preparation": "Extract clear gel from leaf and apply cool gel to burn area"}, {"name": "Digestive aid", "usage_frequency": "high", "description": "Soothes stomach ulcers, acid reflux, and digestive issues", "preparation": "Drink 1-2 tablespoons of pure aloe juice daily before meals"}, {"name": "Anti-inflammatory", "usage_frequency": "high", "description": "Reduces inflammation both internally and externally", "preparation": "Apply gel topically or consume 1 tablespoon aloe juice twice daily"}], "distinguishing_features": {"leaves": "Thick, fleshy, succulent leaves with serrated edges", "arrangement": "Rosette pattern growing from center", "color": "Green to grey-green, young plants have white spots", "texture": "Smooth, waxy surface with clear gel inside when cut", "size": "Leaves can grow 12-24 inches long, 2-3 inches wide", "unique_identifier": "Clear, thick, sticky gel when leaf is broken"}}, "tulsi": {"scientific_name": "Ocimum tenuiflorum", "local_name": "Holy Basil", "hindi_name": "<PERSON><PERSON><PERSON>", "tamil_name": "<PERSON><PERSON><PERSON>", "telugu_name": "<PERSON><PERSON><PERSON>", "bengali_name": "<PERSON><PERSON><PERSON>", "marathi_name": "<PERSON><PERSON>", "gujarati_name": "<PERSON><PERSON><PERSON>", "common_names": ["Holy Basil", "Sacred Basil", "<PERSON><PERSON><PERSON>", "Queen of Herbs"], "medicinal_features": [{"name": "Immunity booster", "usage_frequency": "very_high", "description": "Strengthens immune system and fights infections", "preparation": "Chew 5-7 fresh leaves daily or drink tulsi tea"}, {"name": "Respiratory ailments", "usage_frequency": "very_high", "description": "Treats cough, cold, asthma, and bronchitis", "preparation": "Boil 10-15 leaves in water, add honey, drink warm"}, {"name": "Stress relief", "usage_frequency": "high", "description": "Reduces stress, anxiety, and promotes mental clarity", "preparation": "Drink tulsi tea twice daily or chew fresh leaves"}, {"name": "Fever reducer", "usage_frequency": "high", "description": "Natural antipyretic, reduces body temperature", "preparation": "Boil leaves with ginger, drink decoction 3 times daily"}], "distinguishing_features": {"leaves": "Oval-shaped with serrated edges, aromatic", "arrangement": "Opposite leaves on square stems", "color": "Green to purple-green depending on variety", "smell": "Strong, clove-like fragrance when crushed", "flowers": "Small purple or white flowers in spikes", "unique_identifier": "Distinctive strong aroma and square stem"}}, "neem": {"scientific_name": "Azadirachta indica", "local_name": "<PERSON><PERSON><PERSON>", "hindi_name": "<PERSON><PERSON><PERSON>", "tamil_name": "Vembu", "telugu_name": "Vepa", "bengali_name": "<PERSON><PERSON>", "marathi_name": "<PERSON><PERSON><PERSON><PERSON>", "gujarati_name": "<PERSON><PERSON>", "common_names": ["<PERSON><PERSON><PERSON>", "Indian Lilac", "Margosa Tree"], "medicinal_features": [{"name": "Antibacterial", "usage_frequency": "very_high", "description": "Powerful natural antibiotic and antiseptic", "preparation": "Apply neem oil or paste of crushed leaves to affected area"}, {"name": "Skin conditions", "usage_frequency": "very_high", "description": "Treats acne, eczema, psoriasis, and fungal infections", "preparation": "Make paste of neem leaves, apply to skin for 15-20 minutes"}, {"name": "Blood purifier", "usage_frequency": "high", "description": "Purifies blood and improves overall health", "preparation": "Drink neem juice (1-2 teaspoons) with water on empty stomach"}, {"name": "Diabetes control", "usage_frequency": "medium", "description": "Helps regulate blood sugar levels", "preparation": "Chew 4-5 fresh neem leaves daily or drink neem tea"}], "distinguishing_features": {"leaves": "Compound leaves with 8-19 leaflets, serrated edges", "arrangement": "Alternate, pinnately compound", "color": "Dark green, glossy", "taste": "Extremely bitter", "bark": "Dark brown, deeply furrowed", "unique_identifier": "Intensely bitter taste of all parts"}}, "turmeric": {"scientific_name": "<PERSON><PERSON><PERSON><PERSON> longa", "local_name": "<PERSON><PERSON><PERSON>", "hindi_name": "<PERSON><PERSON>", "tamil_name": "<PERSON><PERSON><PERSON>", "telugu_name": "<PERSON><PERSON><PERSON>", "bengali_name": "<PERSON><PERSON><PERSON>", "marathi_name": "<PERSON><PERSON>", "gujarati_name": "<PERSON><PERSON>", "common_names": ["<PERSON><PERSON><PERSON>", "Golden Spice", "Indian Saffron"], "medicinal_features": [{"name": "Anti-inflammatory", "usage_frequency": "very_high", "description": "Powerful anti-inflammatory agent for joints and muscles", "preparation": "Mix 1 tsp turmeric powder in warm milk, drink daily"}, {"name": "Wound healing", "usage_frequency": "very_high", "description": "Accelerates healing of cuts, wounds, and injuries", "preparation": "Apply turmeric paste directly to clean wound"}, {"name": "Antioxidant", "usage_frequency": "high", "description": "Fights free radicals and boosts immunity", "preparation": "Add turmeric to daily cooking or drink turmeric tea"}, {"name": "Digestive aid", "usage_frequency": "high", "description": "Improves digestion and reduces stomach inflammation", "preparation": "Take 1/2 tsp turmeric powder with warm water before meals"}], "distinguishing_features": {"rhizome": "Orange-yellow underground stem (main medicinal part)", "leaves": "Large, broad, lance-shaped leaves", "color": "Bright yellow-orange when cut", "smell": "Earthy, slightly bitter aroma", "staining": "Stains everything yellow-orange", "unique_identifier": "Bright yellow-orange color and staining property"}}, "ginger": {"scientific_name": "Zingiber officinale", "local_name": "<PERSON>", "hindi_name": "Adrak", "tamil_name": "Inji", "telugu_name": "<PERSON><PERSON>", "bengali_name": "Ada", "marathi_name": "Ale", "gujarati_name": "<PERSON><PERSON>", "common_names": ["<PERSON>", "Fresh Ginger", "Common Ginger"], "medicinal_features": [{"name": "Digestive aid", "usage_frequency": "very_high", "description": "Relieves nausea, indigestion, and stomach upset", "preparation": "Chew small piece of fresh ginger or drink ginger tea"}, {"name": "Anti-nausea", "usage_frequency": "very_high", "description": "Prevents motion sickness and morning sickness", "preparation": "Suck on small piece of fresh ginger or ginger candy"}, {"name": "Cold and flu", "usage_frequency": "high", "description": "Relieves cold symptoms and boosts immunity", "preparation": "Boil ginger with honey and lemon, drink warm"}, {"name": "Anti-inflammatory", "usage_frequency": "high", "description": "Reduces inflammation and joint pain", "preparation": "Drink ginger tea 2-3 times daily or add to cooking"}], "distinguishing_features": {"rhizome": "Knobby, beige-colored underground stem", "texture": "Firm, fibrous when fresh", "smell": "Strong, spicy, aromatic", "taste": "Hot, pungent, slightly sweet", "skin": "Thin, papery brown skin", "unique_identifier": "Characteristic spicy aroma and hot taste"}}}