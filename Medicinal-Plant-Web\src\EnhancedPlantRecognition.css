/* Enhanced Plant Recognition Styles */

.enhanced-plant-recognition {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  color: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5em;
  font-weight: 600;
}

.header p {
  margin: 0;
  font-size: 1.1em;
  opacity: 0.9;
}

.controls {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.system-toggle label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  cursor: pointer;
}

.confidence-control {
  flex: 1;
  min-width: 200px;
}

.confidence-control label {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-weight: 500;
}

.confidence-control input[type="range"] {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #ddd;
  outline: none;
}

.upload-section {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  align-items: center;
  flex-wrap: wrap;
}

.upload-section button {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  background: #4CAF50;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.upload-section button:hover {
  background: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.upload-section button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.camera-controls {
  display: flex;
  gap: 10px;
}

.selected-file {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  background: #e8f5e8;
  border-radius: 8px;
}

.camera-preview video {
  max-width: 100%;
  height: 300px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.error-message {
  padding: 15px;
  background: #ffebee;
  border: 1px solid #f44336;
  border-radius: 8px;
  color: #c62828;
  margin-bottom: 20px;
}

/* Prediction Results */
.prediction-result {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

.result-header {
  padding: 25px;
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  color: white;
}

.result-header h1, .result-header h2 {
  margin: 0 0 15px 0;
  font-size: 2.2em;
}

.identification-info {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 15px;
  align-items: start;
}

.scientific-name, .local-name, .family {
  margin: 5px 0;
  font-size: 1.1em;
}

.confidence-display {
  display: flex;
  justify-content: flex-end;
}

.confidence-badge {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 1.1em;
}

.confidence-badge.high {
  background: #4CAF50;
  color: white;
}

.confidence-badge.medium {
  background: #FF9800;
  color: white;
}

.confidence-badge.low {
  background: #f44336;
  color: white;
}

.local-names {
  margin-top: 15px;
}

.names-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.name-tag {
  background: rgba(255,255,255,0.2);
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.9em;
  border: 1px solid rgba(255,255,255,0.3);
}

/* Content Sections */
.content-tabs {
  padding: 0;
}

.section {
  padding: 25px;
  border-bottom: 1px solid #eee;
}

.section:last-child {
  border-bottom: none;
}

.section h2 {
  color: #2E7D32;
  margin: 0 0 20px 0;
  font-size: 1.8em;
  border-bottom: 2px solid #4CAF50;
  padding-bottom: 10px;
}

.section h3 {
  color: #388E3C;
  margin: 15px 0 10px 0;
}

/* Modern Benefits */
.benefits-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.benefit-tag {
  background: #E8F5E8;
  color: #2E7D32;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.9em;
  border: 1px solid #C8E6C9;
}

/* Traditional Systems */
.traditional-systems {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.system-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #4CAF50;
}

.system-card h3 {
  color: #2E7D32;
  margin: 0 0 15px 0;
  font-size: 1.3em;
}

/* Preparation Methods */
.preparation-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.method-card {
  background: #f0f8f0;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #C8E6C9;
}

.method-card h4 {
  color: #2E7D32;
  margin: 0 0 10px 0;
  font-size: 1.1em;
}

/* Safety Information */
.safety-warnings {
  margin-top: 15px;
}

.warning-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 10px;
  padding: 10px;
  background: #fff3e0;
  border-radius: 8px;
  border-left: 4px solid #FF9800;
}

.warning-icon {
  font-size: 1.2em;
  margin-top: 2px;
}

.general-advice {
  margin-top: 15px;
  padding: 15px;
  background: #e3f2fd;
  border-radius: 8px;
  border-left: 4px solid #2196F3;
}

/* Geography */
.geography-info p {
  margin: 8px 0;
  font-size: 1.05em;
}

/* Low Confidence Results */
.prediction-result.low-confidence .result-header {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}

.top-matches {
  padding: 25px;
}

.match-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 15px;
  border-left: 4px solid #FF9800;
}

.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.match-header h4 {
  margin: 0;
  color: #F57C00;
  font-size: 1.2em;
}

.key-uses, .features {
  margin: 10px 0;
}

.features ul {
  margin: 5px 0;
  padding-left: 20px;
}

.recommendation {
  padding: 25px;
  background: #f0f8f0;
}

.recommendation h3 {
  color: #2E7D32;
  margin: 0 0 15px 0;
}

.tips ul {
  margin: 10px 0;
  padding-left: 20px;
}

/* Unknown Plant Results */
.prediction-result.unknown .result-header {
  background: linear-gradient(135deg, #9E9E9E, #616161);
}

/* Formatted Display */
.formatted-display {
  padding: 25px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
}

.formatted-display h3 {
  color: #2E7D32;
  margin: 0 0 15px 0;
}

.formatted-display pre {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #ddd;
  overflow-x: auto;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

/* Basic Results */
.prediction-result.basic {
  background: #f8f9fa;
}

.basic-content {
  padding: 25px;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.feature-tag {
  background: #E8F5E8;
  color: #2E7D32;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.9em;
  border: 1px solid #C8E6C9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-plant-recognition {
    padding: 10px;
  }
  
  .header h1 {
    font-size: 2em;
  }
  
  .controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .upload-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .identification-info {
    grid-template-columns: 1fr;
  }
  
  .traditional-systems {
    grid-template-columns: 1fr;
  }
  
  .preparation-methods {
    grid-template-columns: 1fr;
  }
  
  .names-grid {
    flex-direction: column;
  }
}
