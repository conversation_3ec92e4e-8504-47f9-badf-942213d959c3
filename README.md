# Medicinal Plant Recognition System

A web-based application for identifying medicinal plants from photos using machine learning.

## Features

- **Image Upload**: Upload photos of medicinal plants
- **Plant Recognition**: Get predictions with confidence scores
- **Enhanced Plant Information**:
  - Scientific names
  - Real/common names
  - Local names
  - Multiple common name variations
- **Medicine Usage Intelligence**:
  - 🌟 **Most Used Medicine**: Highlights the primary medicinal use
  - 🏆 **Popular Uses**: Shows most frequently used treatments
  - **Usage Frequency**: Indicates how commonly each medicine is used (Very High 🔥, High ⭐, Medium, Low)
  - **Detailed Descriptions**: Explains what each medicinal feature does
- **Visual Enhancements**:
  - Color-coded medicine popularity
  - Highlighted high-usage treatments
  - Organized information display
- **Save Records**: Save plant identification records
- **Feedback System**: Provide feedback on predictions
- **Mock Prediction**: Currently uses a mock system with 30 medicinal plants

## Project Structure

```
Medicinal/
├── Medicinal-Plant-Backend/     # Flask API backend
│   ├── app.py                   # Main Flask application
│   ├── models/                  # Model files and classes
│   │   └── classes.json         # Plant class definitions
│   └── requirements.txt         # Python dependencies
├── Medicinal-Plant-Web/         # React frontend
│   ├── src/                     # Source code
│   │   ├── App.jsx             # Main React component
│   │   ├── api.js              # API communication
│   │   └── styles.css          # Styling
│   ├── package.json            # Node.js dependencies
│   └── .env                    # Environment variables
└── test_api.py                 # API testing script
```

## Setup and Installation

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd Medicinal-Plant-Backend
   ```

2. Install Python dependencies:
   ```bash
   pip install flask flask-cors pillow
   ```

3. Run the backend server:
   ```bash
   python app.py
   ```

   The backend will start on `http://localhost:5000`

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd Medicinal-Plant-Web
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

   The frontend will start on `http://localhost:5173`

## Usage

1. **Start both servers** (backend and frontend)
2. **Open your browser** and go to `http://localhost:5173`
3. **Upload an image** of a medicinal plant
4. **Click "Analyze"** to get the prediction
5. **View the enhanced results** including:
   - Scientific name
   - Real/common name
   - Local name
   - All common name variations
   - 🌟 **Most Used Medicine** (highlighted in green)
   - 🏆 **Most Popular Uses** (color-coded badges)
   - **All Medicinal Features** with:
     - Usage frequency indicators (🔥 Very Popular, ⭐ Popular)
     - Detailed descriptions of each medicine
     - Color-coded importance levels
   - Confidence score
6. **Edit information** if needed and save records
7. **Provide feedback** to improve the system

## API Endpoints

### Backend API (`http://localhost:5000`)

- `GET /` - API status check
- `POST /api/predict` - Upload image for plant recognition
- `POST /api/save` - Save plant record
- `POST /api/feedback` - Submit feedback

### Example API Usage

```bash
# Test API status
curl http://localhost:5000/

# Test image prediction (requires image file)
curl -X POST -F "image=@plant_photo.jpg" http://localhost:5000/api/predict
```

## Testing

Run the included test script to verify all API endpoints:

```bash
python test_api.py
```

## Current Status

- ✅ **Backend API** working with enhanced mock predictions
- ✅ **Frontend interface** with beautiful enhanced UI
- ✅ **Image upload and validation** working perfectly
- ✅ **Enhanced plant information display** with:
  - Real names and common names
  - Medicine usage frequency indicators
  - Most used medicine highlighting
  - Detailed medicinal descriptions
  - Color-coded popularity system
- ✅ **Save and feedback functionality** working
- ✅ **30 medicinal plants** with comprehensive data
- ⚠️ Using mock predictions (TensorFlow model not available)

## Sample Plants in Database

The system includes 30 medicinal plants with detailed information:

1. **Aloe Vera** (True Aloe) - 🌟 Most used for: Skin care and burn treatment
2. **Andrographis paniculata** (King of Bitters) - 🌟 Most used for: Immune support and fever treatment
3. **Alpinia officinarum** (Lesser Galangal) - 🌟 Most used for: Digestive health and nausea relief
4. **Artemisia vulgaris** (Common Mugwort) - 🌟 Most used for: Women's health and menstrual support
5. **Asparagus officinalis** (Garden Asparagus) - 🌟 Most used for: Diuretic and kidney support
6. **Bidens pilosa** (Blackjack) - 🌟 Most used for: Wound healing and first aid
7. **Iris domestica** (Blackberry Lily) - 🌟 Most used for: Respiratory and throat treatment

...and 23 more plants with detailed medicinal information!

## Future Improvements

1. **Train actual ML model** with TensorFlow/PyTorch
2. **Add database storage** for records and feedback
3. **Implement user authentication**
4. **Add more plant species**
5. **Improve UI/UX design**
6. **Add mobile app support**

## Troubleshooting

### Common Issues

1. **Backend not starting**: Check Python version and dependencies
2. **Frontend not loading**: Ensure Node.js is installed and run `npm install`
3. **API connection errors**: Verify both servers are running on correct ports
4. **Image upload fails**: Check image format (JPG, PNG supported)

### Dependencies

- **Python 3.x** with Flask, Flask-CORS, Pillow
- **Node.js** with React, Vite
- **Modern web browser** with JavaScript enabled

## License

This project is for educational purposes. Please ensure proper attribution when using plant data.
