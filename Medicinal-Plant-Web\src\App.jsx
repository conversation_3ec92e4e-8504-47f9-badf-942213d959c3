import { useEffect, useMemo, useState } from 'react'
import { predictImage, saveRecord, sendFeedback } from './api'
import './EnhancedPlantRecognition.css'

export default function App() {
  const [file, setFile] = useState(null)
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)
  const [history, setHistory] = useState([])
  const [showCamera, setShowCamera] = useState(false)
  const [stream, setStream] = useState(null)
  const [useEnhancedMode, setUseEnhancedMode] = useState(false)

  const previewUrl = useMemo(() => (file ? URL.createObjectURL(file) : null), [file])
  const [editing, setEditing] = useState(false)
  const [editedFields, setEditedFields] = useState({ scientificName: '', localName: '', medicinalFeature: '' })

  useEffect(() => {
    if (result && !result.error) {
      setEditedFields({
        scientificName: result.scientificName || '',
        localName: result.localName || '',
        medicinalFeature: result.medicinalFeature || ''
      })
      setEditing(false)
    }
  }, [result])

  // Camera functions
  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' } // Use back camera on mobile
      })
      setStream(mediaStream)
      setShowCamera(true)
    } catch (err) {
      alert('Camera access denied or not available')
      console.error('Camera error:', err)
    }
  }

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop())
      setStream(null)
    }
    setShowCamera(false)
  }

  const capturePhoto = () => {
    const video = document.getElementById('camera-video')
    const canvas = document.createElement('canvas')
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight

    const ctx = canvas.getContext('2d')
    ctx.drawImage(video, 0, 0)

    canvas.toBlob((blob) => {
      const capturedFile = new File([blob], 'captured-plant.jpg', { type: 'image/jpeg' })
      setFile(capturedFile)
      stopCamera()
    }, 'image/jpeg', 0.8)
  }

  useEffect(() => {
    return () => {
      // Cleanup camera stream on component unmount
      if (stream) {
        stream.getTracks().forEach(track => track.stop())
      }
    }
  }, [stream])

  // Tab switching function
  const switchTab = (e, tabName) => {
    // Remove active class from all tabs and content
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'))
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'))

    // Add active class to clicked tab and corresponding content
    e.target.classList.add('active')
    document.getElementById(tabName).classList.add('active')
  }

  async function onSubmit(e) {
    e.preventDefault()
    if (!file) return
    setLoading(true)
    setResult(null)
    try {
      const data = await predictImage(file)
      setResult(data)
      setHistory((h) => [{ fileName: file.name, date: new Date().toISOString(), result: data }, ...h].slice(0, 10))
    } catch (err) {
      setResult({ error: err.message || String(err) })
    } finally {
      setLoading(false)
    }
  }

  // Conditional rendering based on mode
  if (useEnhancedMode) {
    return <EnhancedPlantRecognition />
  }

  return (
    <div className="app">
      <header className="header">
        <h1>Medicinal Plant Detection</h1>
        <p className="muted">Upload a leaf or plant photo and get a prediction.</p>

        <div className="mode-toggle">
          <button
            onClick={() => setUseEnhancedMode(true)}
            className="enhanced-mode-btn"
          >
            🚀 Try Enhanced Recognition System
          </button>
        </div>
      </header>

      <main className="main-grid">
        <section className="panel upload">
          <form onSubmit={onSubmit}>
            <div className="upload-options">
              <label className="file-input">
                📁 Upload Image
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => setFile(e.target.files && e.target.files[0])}
                />
              </label>
              <button type="button" onClick={startCamera} className="camera-btn">
                📷 Take Photo
              </button>
            </div>

            {showCamera && (
              <div className="camera-interface">
                <video
                  id="camera-video"
                  autoPlay
                  playsInline
                  ref={(video) => {
                    if (video && stream) {
                      video.srcObject = stream
                    }
                  }}
                  className="camera-video"
                />
                <div className="camera-controls">
                  <button type="button" onClick={capturePhoto} className="capture-btn">
                    📸 Capture
                  </button>
                  <button type="button" onClick={stopCamera} className="cancel-btn">
                    ❌ Cancel
                  </button>
                </div>
              </div>
            )}

            {previewUrl && !showCamera && (
              <div className="preview">
                <img src={previewUrl} alt="preview" />
                <div className="file-meta">{file.name}</div>
              </div>
            )}

            <div className="actions">
              <button type="submit" disabled={loading || !file} className="primary">
                {loading ? 'Analyzing...' : 'Analyze'}
              </button>
              <button type="button" onClick={() => { setFile(null); setResult(null) }} className="ghost">
                Clear
              </button>
            </div>
          </form>

          <div className="history">
            <h3>Recent</h3>
            {history.length === 0 ? <div className="muted">No recent predictions</div> : (
              <ul>
                {history.map((h, i) => (
                  <li key={i}>
                    <strong>{h.fileName}</strong>
                    <div className="muted">{new Date(h.date).toLocaleString()}</div>
                    <div className="history-result">{h.result?.label || h.result?.error || JSON.stringify(h.result)}</div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </section>

        <section className="panel result-panel">
          {!result && <div className="placeholder">No result yet — upload an image to analyze</div>}

          {result && result.error && (
            <div className="error">Error: {result.error}</div>
          )}

          {result && !result.error && (
            <div className="comprehensive-result">
              {/* Header Card */}
              <div className="result-header-card">
                <div className="header-content">
                  <div className="plant-image-section">
                    {previewUrl ? <img src={previewUrl} className="plant-image" alt="plant" /> : (result.imageUrl ? <img src={result.imageUrl} className="plant-image" alt="plant" /> : null)}
                  </div>
                  <div className="plant-info-section">
                    <h1 className="plant-title">
                      {result.realName || result.localName || result.label || 'Unknown'}
                      {result._mock ? <span className="mock-badge">(Demo)</span> : null}
                    </h1>
                    <div className="plant-names">
                      <div><strong>Scientific:</strong> <em>{result.scientificName}</em></div>
                      <div><strong>Local:</strong> {result.localName}</div>
                      {result.commonNames && result.commonNames.length > 0 && (
                        <div><strong>Also known as:</strong> {result.commonNames.join(', ')}</div>
                      )}
                    </div>

                    {result.primaryMedicine && (
                      <div className="primary-medicine">
                        <strong>🌟 Primary Use:</strong> {result.primaryMedicine}
                      </div>
                    )}

                    <div className="confidence-display">
                      <strong>Confidence:</strong> {(result.confidence * 100).toFixed(1)}%
                      <div className="confidence-bar">
                        <div className="confidence-fill" style={{width: `${result.confidence * 100}%`}}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tabbed Content */}
              <div className="tabbed-content">
                <div className="tab-navigation">
                  <button className="tab-btn active" onClick={(e) => switchTab(e, 'overview')}>🌿 Overview</button>
                  <button className="tab-btn" onClick={(e) => switchTab(e, 'traditional')}>🏛️ Traditional Use</button>
                  <button className="tab-btn" onClick={(e) => switchTab(e, 'preparation')}>⚗️ Preparation</button>
                  <button className="tab-btn" onClick={(e) => switchTab(e, 'safety')}>⚠️ Safety</button>
                  <button className="tab-btn" onClick={(e) => switchTab(e, 'geography')}>🌍 Geography</button>
                </div>

                {/* Overview Tab */}
                <div id="overview" className="tab-content active">
                  {result.description && (
                    <div className="description-section">
                      <h3>🔍 Plant Description</h3>
                      <div className="description-grid">
                        <div className="desc-item">
                          <strong>Appearance:</strong>
                          <p>{result.description.appearance}</p>
                        </div>
                        <div className="desc-item">
                          <strong>Habitat:</strong>
                          <p>{result.description.habitat}</p>
                        </div>
                        <div className="desc-item">
                          <strong>Parts Used:</strong>
                          <div className="parts-used">
                            {result.description.plant_parts_used?.map((part, i) => (
                              <span key={i} className="part-badge">{part}</span>
                            ))}
                          </div>
                        </div>
                        <div className="desc-item">
                          <strong>Active Compounds:</strong>
                          <div className="compounds">
                            {result.description.active_compounds?.map((compound, i) => (
                              <span key={i} className="compound-badge">{compound}</span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="medicinal-features-section">
                    <h3>💊 Medicinal Properties</h3>
                    <div className="features-grid">
                      {result.medicinalDetails && Array.isArray(result.medicinalDetails) ?
                        result.medicinalDetails.map((feature, i) => (
                          <div key={i} className={`feature-card ${feature.usage_frequency}`}>
                            <div className="feature-header">
                              <strong>{feature.name}</strong>
                              <span className={`frequency-badge ${feature.usage_frequency}`}>
                                {feature.usage_frequency === 'very_high' ? '🔥 Very Popular' :
                                 feature.usage_frequency === 'high' ? '⭐ Popular' :
                                 feature.usage_frequency === 'medium' ? '📊 Moderate' : '📉 Limited'}
                              </span>
                            </div>
                            <p className="feature-description">{feature.description}</p>
                          </div>
                        )) :
                        <p className="muted">No detailed medicinal information available</p>
                      }
                    </div>
                  </div>
                </div>

                {/* Traditional Medicine Tab */}
                <div id="traditional" className="tab-content">
                  {result.traditionalSystems && Object.keys(result.traditionalSystems).length > 0 ? (
                    <div className="traditional-systems">
                      <h3>🏛️ Traditional Medicine Systems</h3>
                      <div className="systems-grid">
                        {Object.entries(result.traditionalSystems).map(([system, data]) => (
                          <div key={system} className="system-card">
                            <h4 className="system-title">
                              {system === 'ayurveda' ? '🕉️ Ayurveda' :
                               system === 'unani' ? '☪️ Unani' :
                               system === 'tcm' ? '🏮 Traditional Chinese Medicine' :
                               system === 'siddha' ? '🔱 Siddha' : system}
                            </h4>
                            <div><strong>Name:</strong> {data.name}</div>
                            <div><strong>Properties:</strong> {data.properties}</div>
                            <div><strong>Traditional Uses:</strong></div>
                            <ul>
                              {data.uses?.map((use, i) => <li key={i}>{use}</li>)}
                            </ul>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <p className="muted">No traditional medicine information available</p>
                  )}
                </div>

                {/* Preparation Tab */}
                <div id="preparation" className="tab-content">
                  {result.preparationMethods && result.preparationMethods.length > 0 ? (
                    <div className="preparation-methods">
                      <h3>⚗️ Preparation Methods & Dosage</h3>
                      <div className="methods-grid">
                        {result.preparationMethods.map((method, i) => (
                          <div key={i} className="method-card">
                            <h4>{method.method}</h4>
                            <div className="method-details">
                              <div><strong>Preparation:</strong> {method.preparation}</div>
                              <div><strong>Dosage:</strong> {method.dosage}</div>
                              <div><strong>Uses:</strong> {method.uses?.join(', ')}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <p className="muted">No preparation method information available</p>
                  )}
                </div>

                {/* Safety Tab */}
                <div id="safety" className="tab-content">
                  {result.safetyInfo && Object.keys(result.safetyInfo).length > 0 ? (
                    <div className="safety-information">
                      <h3>⚠️ Safety Information</h3>
                      <div className="safety-grid">
                        {result.safetyInfo.side_effects && (
                          <div className="safety-card warning">
                            <h4>⚠️ Possible Side Effects</h4>
                            <ul>
                              {result.safetyInfo.side_effects.map((effect, i) => <li key={i}>{effect}</li>)}
                            </ul>
                          </div>
                        )}
                        {result.safetyInfo.contraindications && (
                          <div className="safety-card danger">
                            <h4>🚫 Contraindications</h4>
                            <ul>
                              {result.safetyInfo.contraindications.map((contra, i) => <li key={i}>{contra}</li>)}
                            </ul>
                          </div>
                        )}
                        {result.safetyInfo.warnings && (
                          <div className="safety-card caution">
                            <h4>⚡ Important Warnings</h4>
                            <ul>
                              {result.safetyInfo.warnings.map((warning, i) => <li key={i}>{warning}</li>)}
                            </ul>
                          </div>
                        )}
                        {result.safetyInfo.toxicity_level && (
                          <div className="safety-card info">
                            <h4>🔬 Toxicity Level</h4>
                            <p><strong>{result.safetyInfo.toxicity_level}</strong></p>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <p className="muted">No safety information available</p>
                  )}
                </div>

                {/* Geography Tab */}
                <div id="geography" className="tab-content">
                  {result.geographicalDistribution && Object.keys(result.geographicalDistribution).length > 0 ? (
                    <div className="geographical-info">
                      <h3>🌍 Geographical Distribution</h3>
                      <div className="geo-grid">
                        {result.geographicalDistribution.native_regions && (
                          <div className="geo-card">
                            <h4>🏠 Native Regions</h4>
                            <p>{result.geographicalDistribution.native_regions.join(', ')}</p>
                          </div>
                        )}
                        {result.geographicalDistribution.cultivated_regions && (
                          <div className="geo-card">
                            <h4>🌱 Cultivated Regions</h4>
                            <p>{result.geographicalDistribution.cultivated_regions.join(', ')}</p>
                          </div>
                        )}
                        {result.geographicalDistribution.climate_zones && (
                          <div className="geo-card">
                            <h4>🌡️ Climate Zones</h4>
                            <p>{result.geographicalDistribution.climate_zones.join(', ')}</p>
                          </div>
                        )}
                        {result.geographicalDistribution.altitude_range && (
                          <div className="geo-card">
                            <h4>⛰️ Altitude Range</h4>
                            <p>{result.geographicalDistribution.altitude_range}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <p className="muted">No geographical information available</p>
                  )}
                </div>
              </div>

              <div style={{ textAlign: 'center', marginTop: 18 }}>
                <div style={{ marginBottom: 8 }}>
                  <button type="button" className="secondary" onClick={() => setEditing((s) => !s)}>
                    {editing ? 'Cancel Edit' : 'Edit Info'}
                  </button>
                </div>

                {editing && (
                  <div style={{ margin: '8px 0' }}>
                    <div style={{ marginBottom: 6 }}>
                      <label style={{ display: 'block', fontSize: 12 }}>Scientific name</label>
                      <input value={editedFields.scientificName} onChange={(e) => setEditedFields(f => ({ ...f, scientificName: e.target.value }))} />
                    </div>
                    <div style={{ marginBottom: 6 }}>
                      <label style={{ display: 'block', fontSize: 12 }}>Local name</label>
                      <input value={editedFields.localName} onChange={(e) => setEditedFields(f => ({ ...f, localName: e.target.value }))} />
                    </div>
                    <div style={{ marginBottom: 6 }}>
                      <label style={{ display: 'block', fontSize: 12 }}>Medicinal features (comma or | separated)</label>
                      <input value={Array.isArray(editedFields.medicinalFeature) ? editedFields.medicinalFeature.join('|') : editedFields.medicinalFeature} onChange={(e) => setEditedFields(f => ({ ...f, medicinalFeature: e.target.value }))} />
                    </div>
                    <div>
                      <button type="button" className="primary" onClick={async () => {
                        // build payload and POST to backend /api/save
                        const mf = (typeof editedFields.medicinalFeature === 'string') ? (editedFields.medicinalFeature.includes('|') ? editedFields.medicinalFeature.split('|') : editedFields.medicinalFeature.split(',').map(s=>s.trim())) : editedFields.medicinalFeature
                        const payload = {
                          class_name: result.label || result.labelName || result.class_name,
                          confidence: result.confidence,
                          scientificName: editedFields.scientificName,
                          localName: editedFields.localName,
                          medicinalFeature: mf
                        }
                        try {
                          await saveRecord(payload)
                          setHistory(h => [{ fileName: file?.name || 'uploaded', date: new Date().toISOString(), saved: true, result: { ...result, ...payload } }, ...h].slice(0, 20))
                          setEditing(false)
                          setResult(r => ({ ...r, ...payload }))
                          window.alert('Saved to backend database')
                        } catch (err) {
                          window.alert('Save failed: ' + (err.message || String(err)))
                        }
                      }}>Save</button>
                    </div>
                  </div>
                )}

                <div style={{ marginTop: 10 }}>
                  <button type="button" className="feedback-btn" onClick={async () => {
                    const fb = window.prompt('Feedback (what is wrong or improvement):')
                    if (fb) {
                      try {
                        await sendFeedback({ fileName: file?.name || 'uploaded', feedback: fb, result })
                        setHistory(h => [{ fileName: file?.name || 'uploaded', date: new Date().toISOString(), feedback: fb, result }, ...h].slice(0, 20))
                        window.alert('Thanks for the feedback — saved to backend.')
                      } catch (err) {
                        window.alert('Feedback send failed: ' + (err.message || String(err)))
                      }
                    }
                  }}>Feedback</button>
                </div>
              </div>
            </div>
          )}
        </section>
      </main>
    </div>
  )
}
