import { useEffect, useMemo, useState } from 'react'
import { predictImage, saveRecord, sendFeedback } from './api'

export default function App() {
  const [file, setFile] = useState(null)
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)
  const [history, setHistory] = useState([])

  const previewUrl = useMemo(() => (file ? URL.createObjectURL(file) : null), [file])
  const [editing, setEditing] = useState(false)
  const [editedFields, setEditedFields] = useState({ scientificName: '', localName: '', medicinalFeature: '' })

  useEffect(() => {
    if (result && !result.error) {
      setEditedFields({
        scientificName: result.scientificName || '',
        localName: result.localName || '',
        medicinalFeature: result.medicinalFeature || ''
      })
      setEditing(false)
    }
  }, [result])

  async function onSubmit(e) {
    e.preventDefault()
    if (!file) return
    setLoading(true)
    setResult(null)
    try {
      const data = await predictImage(file)
      setResult(data)
      setHistory((h) => [{ fileName: file.name, date: new Date().toISOString(), result: data }, ...h].slice(0, 10))
    } catch (err) {
      setResult({ error: err.message || String(err) })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="app">
      <header className="header">
        <h1>Medicinal Plant Detection</h1>
        <p className="muted">Upload a leaf or plant photo and get a prediction.</p>
      </header>

      <main className="main-grid">
        <section className="panel upload">
          <form onSubmit={onSubmit}>
            <label className="file-input">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => setFile(e.target.files && e.target.files[0])}
              />
            </label>

            {previewUrl && (
              <div className="preview">
                <img src={previewUrl} alt="preview" />
                <div className="file-meta">{file.name}</div>
              </div>
            )}

            <div className="actions">
              <button type="submit" disabled={loading || !file} className="primary">
                {loading ? 'Analyzing...' : 'Analyze'}
              </button>
              <button type="button" onClick={() => { setFile(null); setResult(null) }} className="ghost">
                Clear
              </button>
            </div>
          </form>

          <div className="history">
            <h3>Recent</h3>
            {history.length === 0 ? <div className="muted">No recent predictions</div> : (
              <ul>
                {history.map((h, i) => (
                  <li key={i}>
                    <strong>{h.fileName}</strong>
                    <div className="muted">{new Date(h.date).toLocaleString()}</div>
                    <div className="history-result">{h.result?.label || h.result?.error || JSON.stringify(h.result)}</div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </section>

        <section className="panel result-panel">
          {!result && <div className="placeholder">No result yet — upload an image to analyze</div>}

          {result && result.error && (
            <div className="error">Error: {result.error}</div>
          )}

          {result && !result.error && (
            <div>
              <div className="present-card">
                <div className="present-inner">
                  <div className="present-left">
                    <h1 className="present-title">
                      {result.realName || result.localName || result.label || 'Unknown'}
                      {result._mock ? <span style={{ fontSize: 12, color: '#888', marginLeft: 8 }}>(mock)</span> : null}
                    </h1>
                    <div className="present-body">
                      <div><strong>Scientific Name:</strong> {result.scientificName || <span className="muted">—</span>}</div>
                      <div><strong>Real Name:</strong> {result.realName || <span className="muted">—</span>}</div>
                      <div><strong>Local Name:</strong> {result.localName || <span className="muted">—</span>}</div>

                      {result.commonNames && result.commonNames.length > 0 && (
                        <div><strong>Common Names:</strong> {result.commonNames.join(', ')}</div>
                      )}

                      {result.fallback === 'image_hash' ? (
                        <div style={{ marginTop: 6, fontSize: 12, color: '#666' }}>(matched by previous images)</div>
                      ) : null}

                      {result.primaryMedicine && (
                        <div style={{ marginTop: 12, padding: '8px', backgroundColor: '#e8f5e8', borderRadius: '4px', border: '1px solid #4CAF50' }}>
                          <strong style={{ color: '#2E7D32' }}>🌟 Most Used Medicine:</strong>
                          <span style={{ color: '#2E7D32', fontWeight: 'bold' }}> {result.primaryMedicine}</span>
                        </div>
                      )}

                      <div style={{ marginTop: 12 }}><strong>All Medicinal Features:</strong></div>
                      <ul className="medicinal-list">
                        {result.medicinalDetails && Array.isArray(result.medicinalDetails) ?
                          result.medicinalDetails.map((feature, i) => (
                            <li key={i} className={feature.usage_frequency === 'very_high' || feature.usage_frequency === 'high' ? 'high-usage' : ''}>
                              <strong>{feature.name}</strong>
                              {(feature.usage_frequency === 'very_high' || feature.usage_frequency === 'high') &&
                                <span className="usage-badge">{feature.usage_frequency === 'very_high' ? '🔥 Very Popular' : '⭐ Popular'}</span>
                              }
                              <div className="feature-description">{feature.description}</div>
                            </li>
                          )) :
                          (Array.isArray(result.medicinalFeature) ?
                            result.medicinalFeature.map((f, i) => <li key={i}>{f}</li>) :
                            (result.medicinalFeature ? <li>{result.medicinalFeature}</li> : <li className="muted">No data</li>)
                          )
                        }
                      </ul>

                      {result.mostUsedMedicines && result.mostUsedMedicines.length > 0 && (
                        <div style={{ marginTop: 12 }}>
                          <strong>🏆 Most Popular Uses:</strong>
                          <div style={{ marginTop: 4 }}>
                            {result.mostUsedMedicines.map((medicine, i) => (
                              <span key={i} style={{
                                display: 'inline-block',
                                margin: '2px 4px',
                                padding: '4px 8px',
                                backgroundColor: medicine.usage === 'very_high' ? '#FF6B6B' : '#4ECDC4',
                                color: 'white',
                                borderRadius: '12px',
                                fontSize: '12px',
                                fontWeight: 'bold'
                              }}>
                                {medicine.name}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="present-right">
                    {previewUrl ? <img src={previewUrl} className="present-image" alt="plant" /> : (result.imageUrl ? <img src={result.imageUrl} className="present-image" alt="plant" /> : null)}
                  </div>
                </div>
              </div>

              <div style={{ textAlign: 'center', marginTop: 18 }}>
                <div style={{ marginBottom: 8 }}>
                  <button type="button" className="secondary" onClick={() => setEditing((s) => !s)}>
                    {editing ? 'Cancel Edit' : 'Edit Info'}
                  </button>
                </div>

                {editing && (
                  <div style={{ margin: '8px 0' }}>
                    <div style={{ marginBottom: 6 }}>
                      <label style={{ display: 'block', fontSize: 12 }}>Scientific name</label>
                      <input value={editedFields.scientificName} onChange={(e) => setEditedFields(f => ({ ...f, scientificName: e.target.value }))} />
                    </div>
                    <div style={{ marginBottom: 6 }}>
                      <label style={{ display: 'block', fontSize: 12 }}>Local name</label>
                      <input value={editedFields.localName} onChange={(e) => setEditedFields(f => ({ ...f, localName: e.target.value }))} />
                    </div>
                    <div style={{ marginBottom: 6 }}>
                      <label style={{ display: 'block', fontSize: 12 }}>Medicinal features (comma or | separated)</label>
                      <input value={Array.isArray(editedFields.medicinalFeature) ? editedFields.medicinalFeature.join('|') : editedFields.medicinalFeature} onChange={(e) => setEditedFields(f => ({ ...f, medicinalFeature: e.target.value }))} />
                    </div>
                    <div>
                      <button type="button" className="primary" onClick={async () => {
                        // build payload and POST to backend /api/save
                        const mf = (typeof editedFields.medicinalFeature === 'string') ? (editedFields.medicinalFeature.includes('|') ? editedFields.medicinalFeature.split('|') : editedFields.medicinalFeature.split(',').map(s=>s.trim())) : editedFields.medicinalFeature
                        const payload = {
                          class_name: result.label || result.labelName || result.class_name,
                          confidence: result.confidence,
                          scientificName: editedFields.scientificName,
                          localName: editedFields.localName,
                          medicinalFeature: mf
                        }
                        try {
                          await saveRecord(payload)
                          setHistory(h => [{ fileName: file?.name || 'uploaded', date: new Date().toISOString(), saved: true, result: { ...result, ...payload } }, ...h].slice(0, 20))
                          setEditing(false)
                          setResult(r => ({ ...r, ...payload }))
                          window.alert('Saved to backend database')
                        } catch (err) {
                          window.alert('Save failed: ' + (err.message || String(err)))
                        }
                      }}>Save</button>
                    </div>
                  </div>
                )}

                <div style={{ marginTop: 10 }}>
                  <button type="button" className="feedback-btn" onClick={async () => {
                    const fb = window.prompt('Feedback (what is wrong or improvement):')
                    if (fb) {
                      try {
                        await sendFeedback({ fileName: file?.name || 'uploaded', feedback: fb, result })
                        setHistory(h => [{ fileName: file?.name || 'uploaded', date: new Date().toISOString(), feedback: fb, result }, ...h].slice(0, 20))
                        window.alert('Thanks for the feedback — saved to backend.')
                      } catch (err) {
                        window.alert('Feedback send failed: ' + (err.message || String(err)))
                      }
                    }
                  }}>Feedback</button>
                </div>
              </div>
            </div>
          )}
        </section>
      </main>
    </div>
  )
}
