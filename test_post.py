import requests, json
url = 'http://127.0.0.1:5000/api/predict'
fp = r'C:\Users\<USER>\Documents\Medicinal\Medicinal-Plant-Backend\data\oneplant\train\aloe_vera\aloe_vera_0.jpg'
with open(fp, 'rb') as f:
    r = requests.post(url, files={'image': f}, timeout=30)
    print('status', r.status_code)
    try:
        print(json.dumps(r.json(), indent=2))
    except Exception as e:
        print('failed to parse json', e, r.text[:200])
