#!/usr/bin/env python3
"""
🌿 Demo Script for Complete Medicinal Plant Recognition System
Test the enhanced system with sample data and demonstrate all features
"""

import numpy as np
import json
import os
from datetime import datetime

# Import our enhanced modules
try:
    from medicinal_plant_recognition_system import MedicinalPlantRecognitionSystem
    from complete_training_pipeline import CompletePlantRecog<PERSON>tion<PERSON><PERSON>eline
    from enhanced_knowledge_base import EnhancedKnowledgeBase
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Some modules not available: {e}")
    MODULES_AVAILABLE = False

def demo_enhanced_recognition_system():
    """Demonstrate the enhanced recognition system"""
    print("🌿 Enhanced Medicinal Plant Recognition System Demo")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ Required modules not available. Please ensure all files are in the same directory.")
        return
    
    try:
        # Initialize the system
        print("🔧 Initializing Enhanced Recognition System...")
        system = MedicinalPlantRecognitionSystem()
        
        # Test with mock image data
        print("\n📸 Testing with sample plant image...")
        mock_image = np.random.rand(224, 224, 3)
        
        # Test high confidence scenario
        print("\n🔍 Test 1: High Confidence Identification")
        print("-" * 40)
        result = system.identify_plant(mock_image, confidence_threshold=70)
        formatted_output = system.display_formatted_result(result)
        print(formatted_output[:500] + "..." if len(formatted_output) > 500 else formatted_output)
        
        # Test low confidence scenario
        print("\n🔍 Test 2: Low Confidence Scenario")
        print("-" * 40)
        result_low = system.identify_plant(mock_image, confidence_threshold=95)
        formatted_output_low = system.display_formatted_result(result_low)
        print(formatted_output_low[:500] + "..." if len(formatted_output_low) > 500 else formatted_output_low)
        
        print("\n✅ Enhanced Recognition System Demo Completed!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")

def demo_knowledge_base():
    """Demonstrate the enhanced knowledge base"""
    print("\n📚 Enhanced Knowledge Base Demo")
    print("=" * 60)
    
    try:
        # Initialize knowledge base
        kb = EnhancedKnowledgeBase()
        
        # Get statistics
        stats = kb.get_plant_statistics()
        print(f"📊 Knowledge Base Statistics:")
        print(f"  Total Plants: {stats['total_plants']}")
        print(f"  Comprehensive Plants: {stats['comprehensive_plants']}")
        print(f"  Completion Rate: {stats['completion_rate']:.1%}")
        print(f"  Unique Features: {stats['unique_medicinal_features']}")
        
        # Test search functionality
        print(f"\n🔍 Testing Search Functionality:")
        
        # Search by name
        search_results = kb.search_plants("Tulsi")
        print(f"  Search for 'Tulsi': {len(search_results)} results")
        for plant_id, plant, score in search_results[:2]:
            print(f"    - {plant.scientific_name} (Score: {score:.2f})")
        
        # Search by symptoms
        symptom_results = kb.search_by_symptoms(["fever", "cough"])
        print(f"  Search for symptoms 'fever, cough': {len(symptom_results)} results")
        for plant_id, plant, score in symptom_results[:2]:
            print(f"    - {plant.scientific_name} (Score: {score:.2f})")
        
        print("\n✅ Knowledge Base Demo Completed!")
        
    except Exception as e:
        print(f"❌ Knowledge Base demo failed: {e}")

def demo_training_pipeline():
    """Demonstrate the training pipeline (without actual training)"""
    print("\n🚀 Training Pipeline Demo")
    print("=" * 60)
    
    try:
        # Configuration for demo
        config = {
            'architecture': 'efficientnet_b3',
            'input_shape': [224, 224, 3],
            'batch_size': 16,
            'model_name': 'demo_plant_model'
        }
        
        # Initialize pipeline
        print("🔧 Initializing Training Pipeline...")
        pipeline = CompletePlantRecognitionPipeline(config)
        
        # Create model
        print("🏗️ Creating Model Architecture...")
        model, base_model = pipeline.create_model('efficientnet_b3')
        
        print(f"✅ Model created successfully!")
        print(f"  Total Parameters: {model.count_params():,}")
        print(f"  Input Shape: {config['input_shape']}")
        print(f"  Number of Classes: {len(pipeline.class_names)}")
        
        # Setup post-processing
        print("⚙️ Setting up Post-processing...")
        pipeline.setup_post_processing()
        
        # Generate deployment report
        print("📋 Generating Deployment Report...")
        report = pipeline.generate_deployment_report()
        
        print(f"📊 Deployment Recommendations:")
        for platform, recommendation in report['deployment_recommendations'].items():
            print(f"  {platform.title()}: {recommendation}")
        
        print("\n✅ Training Pipeline Demo Completed!")
        
    except Exception as e:
        print(f"❌ Training Pipeline demo failed: {e}")

def demo_api_integration():
    """Demonstrate API integration"""
    print("\n🌐 API Integration Demo")
    print("=" * 60)
    
    try:
        # Check if backend files exist
        backend_files = [
            "Medicinal-Plant-Backend/app.py",
            "Medicinal-Plant-Backend/models/classes.json"
        ]
        
        missing_files = [f for f in backend_files if not os.path.exists(f)]
        
        if missing_files:
            print(f"⚠️ Missing backend files: {missing_files}")
            print("Please ensure the backend is properly set up.")
        else:
            print("✅ Backend files found!")
            
            # Check frontend files
            frontend_files = [
                "Medicinal-Plant-Web/src/App.jsx",
                "Medicinal-Plant-Web/src/EnhancedPlantRecognition.jsx"
            ]
            
            missing_frontend = [f for f in frontend_files if not os.path.exists(f)]
            
            if missing_frontend:
                print(f"⚠️ Missing frontend files: {missing_frontend}")
            else:
                print("✅ Frontend files found!")
                print("\n🚀 To start the complete system:")
                print("1. Backend: cd Medicinal-Plant-Backend && python app.py")
                print("2. Frontend: cd Medicinal-Plant-Web && npm run dev")
                print("3. Open browser to http://localhost:5173")
                print("4. Click 'Try Enhanced Recognition System' button")
        
        print("\n✅ API Integration Check Completed!")
        
    except Exception as e:
        print(f"❌ API Integration demo failed: {e}")

def create_sample_config():
    """Create a sample configuration file"""
    config = {
        "architecture": "efficientnet_b3",
        "input_shape": [224, 224, 3],
        "batch_size": 32,
        "learning_rate": 0.001,
        "epochs": {
            "phase_1": 20,
            "phase_2": 15,
            "phase_3": 10
        },
        "confidence_thresholds": {
            "very_high": 0.95,
            "high": 0.80,
            "medium": 0.60,
            "minimum_display": 0.70
        },
        "data_paths": {
            "train_dir": "data/train",
            "val_dir": "data/val",
            "test_dir": "data/test"
        },
        "model_name": "medicinal_plant_model",
        "export_formats": ["savedmodel", "tflite"]
    }
    
    with open("config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("📄 Sample configuration file created: config.json")

def main():
    """Main demo function"""
    print("🌿 COMPLETE MEDICINAL PLANT RECOGNITION SYSTEM DEMO")
    print("=" * 80)
    print(f"Demo started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all demos
    demo_enhanced_recognition_system()
    demo_knowledge_base()
    demo_training_pipeline()
    demo_api_integration()
    
    # Create sample config
    print("\n📄 Creating Sample Configuration...")
    create_sample_config()
    
    print("\n" + "=" * 80)
    print("🎉 COMPLETE DEMO FINISHED!")
    print("\n📋 Summary of Available Components:")
    print("✅ Enhanced Recognition System - Advanced plant identification")
    print("✅ Knowledge Base - Comprehensive plant information")
    print("✅ Training Pipeline - Complete model training workflow")
    print("✅ Post-processing - Intelligent confidence handling")
    print("✅ Web Interface - User-friendly frontend")
    print("✅ API Backend - RESTful API endpoints")
    
    print("\n🚀 Next Steps:")
    print("1. Organize your plant images in data/train, data/val, data/test")
    print("2. Run: python complete_training_pipeline.py --architecture efficientnet_b3")
    print("3. Start the backend: cd Medicinal-Plant-Backend && python app.py")
    print("4. Start the frontend: cd Medicinal-Plant-Web && npm run dev")
    print("5. Test with real plant images!")
    
    print("\n💡 For production deployment:")
    print("- Use the enhanced prediction endpoint: /api/predict_enhanced")
    print("- Set appropriate confidence thresholds (recommended: 80%)")
    print("- Monitor performance and collect user feedback")
    print("- Regularly retrain with new data")

if __name__ == "__main__":
    main()
