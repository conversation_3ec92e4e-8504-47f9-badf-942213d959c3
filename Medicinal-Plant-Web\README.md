# Medicinal Plant Web (frontend)

This is a small React + Vite frontend for the Medicinal Plant Recognition system.

Prerequisites
- Node.js 18.x or 20.x (LTS)
- npm (comes with Node.js)

Install and run

1. Open this folder in VS Code.
2. Install dependencies:

```powershell
npm install
```

3. Start dev server:

```powershell
npm run dev
```

This starts a dev server on http://localhost:5173 by default. The app will POST images to `/api/predict`. If your backend runs on a different origin, either run the backend on the same host or update `src/api.js` to point to the backend URL.

Configuring backend URL
- Create a `.env` file in the project root with the variable `VITE_BACKEND_URL` (e.g. `VITE_BACKEND_URL=http://localhost:5000`). Vite exposes variables prefixed with `VITE_` to the client.

Quick mock backend (optional)
1. Create a small Express server that returns a fake prediction (I can scaffold this for you). Run it on port 5000 and the frontend will call it if you set `VITE_BACKEND_URL=http://localhost:5000`.

