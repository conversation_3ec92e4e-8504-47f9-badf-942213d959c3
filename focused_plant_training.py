#!/usr/bin/env python3
"""
🌿 Focused Plant Training System
Train model properly with multiple angles of one plant (<PERSON><PERSON> example)
Ensure accurate scientific name, local name, and medicinal features
"""

import os
import json
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, Model
from tensorflow.keras.applications import EfficientNetB0
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix
import cv2
from PIL import Image
import requests
from io import BytesIO

class FocusedPlantTrainer:
    """Focused training system for accurate plant recognition"""
    
    def __init__(self):
        self.model = None
        self.class_names = []
        self.plant_database = self._load_plant_database()
        
    def _load_plant_database(self):
        """Load accurate plant database"""
        return {
            "aloe_vera": {
                "scientific_name": "Aloe vera",
                "local_name": "Aloe Vera",
                "hindi_name": "<PERSON><PERSON><PERSON>kumar<PERSON>",
                "tamil_name": "<PERSON><PERSON><PERSON>",
                "telugu_name": "Kalabanda",
                "common_names": ["Aloe Vera", "True Aloe", "Medicinal Aloe", "Barbados Aloe"],
                "medicinal_features": [
                    {
                        "name": "Skin healing",
                        "usage_frequency": "very_high",
                        "description": "Heals cuts, burns, wounds, and skin irritations",
                        "preparation": "Apply fresh gel directly to affected area"
                    },
                    {
                        "name": "Burns treatment", 
                        "usage_frequency": "very_high",
                        "description": "Excellent for sunburn and minor burns",
                        "preparation": "Extract gel from leaf and apply cool gel to burn"
                    },
                    {
                        "name": "Digestive aid",
                        "usage_frequency": "high", 
                        "description": "Soothes stomach ulcers and digestive issues",
                        "preparation": "Drink 1-2 tablespoons of aloe juice daily"
                    },
                    {
                        "name": "Anti-inflammatory",
                        "usage_frequency": "high",
                        "description": "Reduces inflammation both internally and externally",
                        "preparation": "Apply gel topically or consume aloe juice"
                    }
                ],
                "distinguishing_features": {
                    "leaves": "Thick, fleshy, succulent leaves with serrated edges",
                    "arrangement": "Rosette pattern from center",
                    "color": "Green to grey-green with white spots on young plants",
                    "texture": "Smooth, waxy surface with gel inside",
                    "size": "Can grow 12-24 inches long",
                    "unique_identifier": "Clear, thick gel when leaf is cut"
                },
                "safety_info": {
                    "safe_uses": ["Topical application", "Small amounts of gel"],
                    "warnings": ["Avoid latex (yellow sap)", "Pregnant women should avoid internal use"],
                    "side_effects": ["Possible skin irritation in sensitive individuals"]
                }
            }
        }
    
    def create_training_data_structure(self):
        """Create proper directory structure for training"""
        base_dir = "focused_training_data"
        
        # Create directories
        dirs = [
            f"{base_dir}/train/aloe_vera",
            f"{base_dir}/validation/aloe_vera", 
            f"{base_dir}/test/aloe_vera"
        ]
        
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
            
        print("📁 Training data structure created:")
        print(f"   {base_dir}/train/aloe_vera/")
        print(f"   {base_dir}/validation/aloe_vera/")
        print(f"   {base_dir}/test/aloe_vera/")
        print("\n📸 Please add Aloe Vera images in different angles:")
        print("   - Top view (rosette pattern)")
        print("   - Side view (leaf arrangement)")
        print("   - Close-up of leaves (texture, serrated edges)")
        print("   - Cut leaf showing gel")
        print("   - Different lighting conditions")
        print("   - Different plant sizes/ages")
        
        return base_dir
    
    def generate_sample_images(self, base_dir):
        """Generate sample training images with different angles and augmentations"""
        print("🎨 Generating sample training images with different angles...")
        
        # Create a simple synthetic Aloe Vera representation
        def create_aloe_sample(angle=0, lighting=1.0, size_factor=1.0):
            """Create synthetic Aloe Vera image with different parameters"""
            img = np.zeros((224, 224, 3), dtype=np.uint8)
            
            # Background (soil/pot)
            img[:, :] = [139, 69, 19]  # Brown background
            
            # Center point
            center_x, center_y = 112, 112
            
            # Draw rosette pattern (multiple leaves)
            for i in range(8):  # 8 leaves in rosette
                leaf_angle = (i * 45 + angle) % 360
                
                # Leaf parameters
                leaf_length = int(60 * size_factor)
                leaf_width = int(15 * size_factor)
                
                # Calculate leaf end point
                end_x = center_x + int(leaf_length * np.cos(np.radians(leaf_angle)))
                end_y = center_y + int(leaf_length * np.sin(np.radians(leaf_angle)))
                
                # Draw leaf (green with serrated edges)
                leaf_color = [34, int(139 * lighting), 34]  # Green with lighting
                
                # Draw main leaf body
                cv2.line(img, (center_x, center_y), (end_x, end_y), leaf_color, leaf_width)
                
                # Add serrated edges (small triangular notches)
                for j in range(3):
                    edge_pos = 0.3 + j * 0.2  # Position along leaf
                    edge_x = center_x + int(leaf_length * edge_pos * np.cos(np.radians(leaf_angle)))
                    edge_y = center_y + int(leaf_length * edge_pos * np.sin(np.radians(leaf_angle)))
                    
                    # Small serration
                    perp_angle = leaf_angle + 90
                    serr_x = edge_x + int(3 * np.cos(np.radians(perp_angle)))
                    serr_y = edge_y + int(3 * np.sin(np.radians(perp_angle)))
                    cv2.circle(img, (serr_x, serr_y), 2, leaf_color, -1)
            
            # Add center rosette
            cv2.circle(img, (center_x, center_y), 8, [0, 100, 0], -1)
            
            return img
        
        # Generate training samples
        sample_count = 0
        
        # Different angles
        for angle in [0, 45, 90, 135, 180, 225, 270, 315]:
            for lighting in [0.7, 1.0, 1.3]:  # Different lighting
                for size in [0.8, 1.0, 1.2]:  # Different sizes
                    img = create_aloe_sample(angle, lighting, size)
                    
                    # Determine split (70% train, 20% val, 10% test)
                    if sample_count < 17:
                        split = "train"
                    elif sample_count < 22:
                        split = "validation"
                    else:
                        split = "test"
                    
                    # Save image
                    filename = f"{base_dir}/{split}/aloe_vera/sample_{sample_count:03d}.jpg"
                    cv2.imwrite(filename, img)
                    sample_count += 1
        
        print(f"✅ Generated {sample_count} sample images")
        print(f"   Training: 17 images")
        print(f"   Validation: 5 images") 
        print(f"   Test: 2 images")
        
        return sample_count
    
    def create_model(self):
        """Create focused model for plant recognition"""
        print("🏗️ Creating focused plant recognition model...")
        
        # Use EfficientNetB0 as base (smaller, faster)
        base_model = EfficientNetB0(
            weights='imagenet',
            include_top=False,
            input_shape=(224, 224, 3)
        )
        
        # Freeze base model initially
        base_model.trainable = False
        
        # Add custom classification head
        inputs = tf.keras.Input(shape=(224, 224, 3))
        x = base_model(inputs, training=False)
        x = layers.GlobalAveragePooling2D()(x)
        x = layers.Dropout(0.3)(x)
        x = layers.Dense(128, activation='relu', name='feature_layer')(x)
        x = layers.Dropout(0.2)(x)
        outputs = layers.Dense(1, activation='sigmoid', name='aloe_classifier')(x)  # Binary classification
        
        model = Model(inputs, outputs)
        
        # Compile model
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )
        
        self.model = model
        self.base_model = base_model
        
        print("✅ Model created successfully")
        print(f"   Total parameters: {model.count_params():,}")
        print(f"   Trainable parameters: {sum([tf.keras.backend.count_params(w) for w in model.trainable_weights]):,}")
        
        return model
    
    def train_model(self, data_dir):
        """Train the model with proper data augmentation"""
        print("🚀 Starting focused training...")
        
        # Data generators with augmentation
        train_datagen = ImageDataGenerator(
            rescale=1./255,
            rotation_range=30,
            width_shift_range=0.2,
            height_shift_range=0.2,
            shear_range=0.2,
            zoom_range=0.2,
            horizontal_flip=True,
            brightness_range=[0.8, 1.2],
            fill_mode='nearest'
        )
        
        val_datagen = ImageDataGenerator(rescale=1./255)
        
        # Create generators
        train_generator = train_datagen.flow_from_directory(
            f"{data_dir}/train",
            target_size=(224, 224),
            batch_size=8,
            class_mode='binary',
            classes=['aloe_vera']
        )
        
        validation_generator = val_datagen.flow_from_directory(
            f"{data_dir}/validation", 
            target_size=(224, 224),
            batch_size=8,
            class_mode='binary',
            classes=['aloe_vera']
        )
        
        # Callbacks
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=10,
                restore_best_weights=True
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7
            )
        ]
        
        # Phase 1: Train with frozen base
        print("📚 Phase 1: Training with frozen base model...")
        history1 = self.model.fit(
            train_generator,
            epochs=20,
            validation_data=validation_generator,
            callbacks=callbacks,
            verbose=1
        )
        
        # Phase 2: Fine-tune with unfrozen base
        print("🔧 Phase 2: Fine-tuning with unfrozen base model...")
        self.base_model.trainable = True
        
        # Lower learning rate for fine-tuning
        self.model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.0001),
            loss='binary_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )
        
        history2 = self.model.fit(
            train_generator,
            epochs=10,
            validation_data=validation_generator,
            callbacks=callbacks,
            verbose=1
        )
        
        print("✅ Training completed!")
        return history1, history2
    
    def evaluate_model(self, data_dir):
        """Evaluate model performance"""
        print("📊 Evaluating model performance...")
        
        test_datagen = ImageDataGenerator(rescale=1./255)
        test_generator = test_datagen.flow_from_directory(
            f"{data_dir}/test",
            target_size=(224, 224),
            batch_size=1,
            class_mode='binary',
            classes=['aloe_vera'],
            shuffle=False
        )
        
        # Evaluate
        results = self.model.evaluate(test_generator, verbose=1)
        
        print(f"📈 Test Results:")
        print(f"   Accuracy: {results[1]:.3f}")
        print(f"   Precision: {results[2]:.3f}")
        print(f"   Recall: {results[3]:.3f}")
        
        return results
    
    def predict_plant(self, image_path):
        """Predict plant with detailed information"""
        print(f"🔍 Analyzing plant image: {image_path}")
        
        # Load and preprocess image
        img = tf.keras.preprocessing.image.load_img(image_path, target_size=(224, 224))
        img_array = tf.keras.preprocessing.image.img_to_array(img)
        img_array = np.expand_dims(img_array, axis=0) / 255.0
        
        # Predict
        prediction = self.model.predict(img_array, verbose=0)[0][0]
        confidence = float(prediction)
        
        # Get plant information
        plant_info = self.plant_database["aloe_vera"]
        
        # Format result
        result = {
            "scientific_name": plant_info["scientific_name"],
            "local_name": plant_info["local_name"],
            "hindi_name": plant_info["hindi_name"],
            "tamil_name": plant_info["tamil_name"],
            "telugu_name": plant_info["telugu_name"],
            "confidence": confidence,
            "medicinal_features": plant_info["medicinal_features"],
            "distinguishing_features": plant_info["distinguishing_features"],
            "safety_info": plant_info["safety_info"]
        }
        
        return result
    
    def display_result(self, result):
        """Display result in required format"""
        print("\n" + "="*60)
        print("🌿 PLANT IDENTIFICATION RESULT")
        print("="*60)
        
        print(f"🔬 Scientific Name: {result['scientific_name']}")
        print(f"🏷️  Local Name: {result['local_name']}")
        print(f"🇮🇳 Hindi Name: {result['hindi_name']}")
        print(f"🇮🇳 Tamil Name: {result['tamil_name']}")
        print(f"🇮🇳 Telugu Name: {result['telugu_name']}")
        print(f"📊 Confidence: {result['confidence']:.1%}")
        
        print(f"\n💊 MEDICINAL FEATURES:")
        for feature in result['medicinal_features']:
            print(f"   • {feature['name']} ({feature['usage_frequency']})")
            print(f"     {feature['description']}")
            print(f"     Preparation: {feature['preparation']}")
            print()
        
        print(f"🔍 DISTINGUISHING FEATURES:")
        for key, value in result['distinguishing_features'].items():
            print(f"   • {key.title()}: {value}")
        
        print(f"\n⚠️ SAFETY INFORMATION:")
        print(f"   Safe uses: {', '.join(result['safety_info']['safe_uses'])}")
        print(f"   Warnings: {', '.join(result['safety_info']['warnings'])}")
        
        return result

def main():
    """Main training and testing function"""
    print("🌿 FOCUSED PLANT TRAINING SYSTEM")
    print("="*60)
    
    trainer = FocusedPlantTrainer()
    
    # Step 1: Create data structure
    data_dir = trainer.create_training_data_structure()
    
    # Step 2: Generate sample images
    trainer.generate_sample_images(data_dir)
    
    # Step 3: Create model
    model = trainer.create_model()
    
    # Step 4: Train model
    print("\n🚀 Starting training process...")
    print("Note: In real implementation, replace sample images with actual Aloe Vera photos")
    
    try:
        history1, history2 = trainer.train_model(data_dir)
        
        # Step 5: Evaluate model
        results = trainer.evaluate_model(data_dir)
        
        # Step 6: Save model
        model_path = "focused_aloe_model.h5"
        trainer.model.save(model_path)
        print(f"💾 Model saved to: {model_path}")
        
        # Step 7: Test prediction
        test_image = f"{data_dir}/test/aloe_vera/sample_023.jpg"
        if os.path.exists(test_image):
            result = trainer.predict_plant(test_image)
            trainer.display_result(result)
        
        print("\n✅ TRAINING COMPLETED SUCCESSFULLY!")
        print("\n📋 Next Steps:")
        print("1. Replace sample images with real Aloe Vera photos from different angles")
        print("2. Add more plant species to the database")
        print("3. Retrain with larger dataset")
        print("4. Deploy model to your web application")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        print("💡 Make sure you have sufficient training data")

if __name__ == "__main__":
    main()
