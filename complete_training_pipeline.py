#!/usr/bin/env python3
"""
🌿 Complete Medicinal Plant Recognition Training Pipeline
Comprehensive solution integrating all improvements for robust plant classification
"""

import tensorflow as tf
import numpy as np
import os
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import argparse
import logging

# Import our custom modules
from model_architectures import PlantRecognitionModels, ModelComparison
from training_strategy import PlantTrainingStrategy, TrainingUtils
from evaluation_metrics import PlantEvaluationMetrics
from post_processing import PlantPostProcessor, ImageQualityAssessment
from enhanced_knowledge_base import EnhancedKnowledgeBase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompletePlantRecognitionPipeline:
    """Complete end-to-end training and deployment pipeline"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.model = None
        self.base_model = None
        self.post_processor = None
        self.knowledge_base = None
        self.class_names = []
        
        # Initialize components
        self._setup_directories()
        self._load_class_names()
        self._initialize_knowledge_base()
        
    def _setup_directories(self):
        """Create necessary directories"""
        directories = [
            'models', 'logs', 'evaluation_results', 
            'data/train', 'data/val', 'data/test',
            'exports', 'reports'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        logger.info("📁 Directories created successfully")
    
    def _load_class_names(self):
        """Load class names from configuration or data directory"""
        if 'class_names_file' in self.config:
            with open(self.config['class_names_file'], 'r') as f:
                self.class_names = json.load(f)
        else:
            # Auto-detect from data directory
            train_dir = self.config.get('train_dir', 'data/train')
            if os.path.exists(train_dir):
                self.class_names = sorted([d for d in os.listdir(train_dir) 
                                         if os.path.isdir(os.path.join(train_dir, d))])
            else:
                # Default medicinal plants for demo
                self.class_names = [
                    'Aloe_vera', 'Andrographis_paniculata', 'Plectranthus_amboinicus',
                    'Ocimum_sanctum', 'Curcuma_longa', 'Azadirachta_indica',
                    'Moringa_oleifera', 'Withania_somnifera', 'Centella_asiatica',
                    'Bacopa_monnieri'
                ]
        
        logger.info(f"🌿 Loaded {len(self.class_names)} plant classes")
    
    def _initialize_knowledge_base(self):
        """Initialize enhanced knowledge base"""
        self.knowledge_base = EnhancedKnowledgeBase()
        logger.info("📚 Knowledge base initialized")
    
    def create_model(self, architecture: str = 'efficientnet_b3') -> Tuple[tf.keras.Model, tf.keras.Model]:
        """Create and configure model architecture"""
        logger.info(f"🏗️ Creating {architecture} model...")
        
        model_factory = PlantRecognitionModels(
            num_classes=len(self.class_names),
            input_shape=tuple(self.config.get('input_shape', [224, 224, 3]))
        )
        
        # Create model based on architecture choice
        if architecture.startswith('mobilenet'):
            variant = architecture.split('_')[1] if '_' in architecture else 'large'
            self.model, self.base_model = model_factory.create_mobilenet_v3(variant)
        elif architecture.startswith('efficientnet'):
            variant = architecture.split('_')[1].upper() if '_' in architecture else 'B3'
            self.model, self.base_model = model_factory.create_efficientnet(variant)
        elif architecture.startswith('resnet'):
            variant = architecture.split('_')[1] if '_' in architecture else '50'
            self.model, self.base_model = model_factory.create_resnet(variant)
        else:
            raise ValueError(f"Unsupported architecture: {architecture}")
        
        logger.info(f"✅ Model created with {self.model.count_params():,} parameters")
        return self.model, self.base_model
    
    def train_model(self, train_dir: str, val_dir: str) -> Dict:
        """Execute complete training pipeline"""
        logger.info("🚀 Starting complete training pipeline...")
        
        if self.model is None:
            raise ValueError("Model not created. Call create_model() first.")
        
        # Initialize training strategy
        trainer = PlantTrainingStrategy(self.model, self.base_model, len(self.class_names))
        
        # Execute 3-phase training
        training_history = trainer.full_training_pipeline(
            train_dir=train_dir,
            val_dir=val_dir,
            batch_size=self.config.get('batch_size', 32)
        )
        
        logger.info("✅ Training completed successfully")
        return training_history
    
    def evaluate_model(self, test_dir: str) -> Dict:
        """Comprehensive model evaluation"""
        logger.info("🔬 Starting comprehensive evaluation...")
        
        if self.model is None:
            raise ValueError("Model not trained. Train model first.")
        
        # Create test data generator
        test_datagen = tf.keras.preprocessing.image.ImageDataGenerator(rescale=1./255)
        test_generator = test_datagen.flow_from_directory(
            test_dir,
            target_size=self.config.get('input_shape', [224, 224])[:2],
            batch_size=32,
            class_mode='categorical',
            shuffle=False
        )
        
        # Initialize evaluator
        evaluator = PlantEvaluationMetrics(
            class_names=self.class_names,
            model_name=self.config.get('model_name', 'plant_model')
        )
        
        # Run comprehensive evaluation
        evaluation_results = evaluator.evaluate_model(self.model, test_generator)
        
        logger.info("✅ Evaluation completed successfully")
        return evaluation_results
    
    def setup_post_processing(self, confidence_thresholds: Optional[Dict] = None):
        """Setup intelligent post-processing"""
        logger.info("⚙️ Setting up post-processing...")
        
        # Use custom thresholds or defaults
        thresholds = confidence_thresholds or {
            'very_high': 0.95,
            'high': 0.80,
            'medium': 0.60,
            'low': 0.40,
            'minimum_display': 0.70,  # Higher threshold for medicinal plants
            'uncertainty_threshold': 0.25
        }
        
        self.post_processor = PlantPostProcessor(
            class_names=self.class_names,
            confidence_thresholds=thresholds
        )
        
        logger.info("✅ Post-processing configured")
    
    def predict_with_intelligence(self, image: np.ndarray, 
                                return_alternatives: bool = True) -> Dict:
        """Intelligent prediction with comprehensive analysis"""
        if self.model is None or self.post_processor is None:
            raise ValueError("Model and post-processor must be initialized")
        
        # Preprocess image
        if len(image.shape) == 3:
            image = np.expand_dims(image, axis=0)
        
        # Get model predictions
        predictions = self.model.predict(image, verbose=0)[0]
        
        # Assess image quality
        quality_score = ImageQualityAssessment.assess_quality(image[0])
        
        # Process with intelligent post-processing
        result = self.post_processor.process_prediction(predictions, quality_score)
        
        # Get comprehensive plant information
        plant_info = None
        if result.should_show_result:
            # Find plant in knowledge base
            search_results = self.knowledge_base.search_plants(result.predicted_class)
            if search_results:
                plant_id, plant_info, _ = search_results[0]
        
        # Compile comprehensive response
        response = {
            'prediction': {
                'class': result.predicted_class,
                'confidence': result.confidence,
                'confidence_level': result.confidence_level.value,
                'should_show': result.should_show_result
            },
            'recommendation': result.recommendation,
            'top_predictions': result.top_k_predictions,
            'uncertainty_score': result.uncertainty_score,
            'image_quality': quality_score,
            'metadata': result.metadata
        }
        
        # Add plant information if available
        if plant_info:
            response['plant_info'] = {
                'scientific_name': plant_info.scientific_name,
                'common_names': plant_info.common_names,
                'description': plant_info.description,
                'medicinal_uses': plant_info.features,
                'traditional_systems': plant_info.traditional_systems,
                'preparation_methods': plant_info.preparation_methods,
                'safety_info': plant_info.safety_info,
                'most_used_medicine': plant_info.most_used_medicine
            }
        
        # Add alternatives if requested
        if return_alternatives and result.alternative_suggestions:
            response['alternatives'] = result.alternative_suggestions
            
            # Get similar plants
            if plant_info:
                similar_plants = self.knowledge_base.get_similar_plants(plant_id)
                response['similar_plants'] = [
                    {
                        'name': plant.scientific_name,
                        'common_name': plant.real_name,
                        'reason': reason
                    }
                    for _, plant, reason in similar_plants
                ]
        
        return response
    
    def export_model(self, export_format: str = 'savedmodel'):
        """Export trained model for deployment"""
        if self.model is None:
            raise ValueError("No model to export")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if export_format == 'savedmodel':
            export_path = f"exports/plant_model_{timestamp}"
            self.model.save(export_path)
            logger.info(f"📦 Model exported to {export_path}")
        
        elif export_format == 'tflite':
            # Convert to TensorFlow Lite for mobile deployment
            converter = tf.lite.TFLiteConverter.from_keras_model(self.model)
            converter.optimizations = [tf.lite.Optimize.DEFAULT]
            tflite_model = converter.convert()
            
            tflite_path = f"exports/plant_model_{timestamp}.tflite"
            with open(tflite_path, 'wb') as f:
                f.write(tflite_model)
            logger.info(f"📱 TFLite model exported to {tflite_path}")
        
        elif export_format == 'onnx':
            # Convert to ONNX for cross-platform deployment
            try:
                import tf2onnx
                onnx_path = f"exports/plant_model_{timestamp}.onnx"
                tf2onnx.convert.from_keras(self.model, output_path=onnx_path)
                logger.info(f"🔄 ONNX model exported to {onnx_path}")
            except ImportError:
                logger.warning("tf2onnx not installed. Install with: pip install tf2onnx")
    
    def generate_deployment_report(self) -> Dict:
        """Generate comprehensive deployment report"""
        if self.model is None:
            raise ValueError("Model not available for report generation")
        
        # Model specifications
        model_specs = ModelComparison.get_model_specs()
        
        # Knowledge base statistics
        kb_stats = self.knowledge_base.get_plant_statistics()
        
        # Post-processing configuration
        pp_config = self.post_processor.thresholds if self.post_processor else {}
        
        report = {
            'model_info': {
                'architecture': self.config.get('architecture', 'unknown'),
                'total_parameters': int(self.model.count_params()),
                'input_shape': self.config.get('input_shape', [224, 224, 3]),
                'num_classes': len(self.class_names)
            },
            'knowledge_base': kb_stats,
            'post_processing': pp_config,
            'deployment_recommendations': {
                'mobile_app': 'Use TFLite model with MobileNetV3',
                'web_app': 'Use SavedModel with EfficientNet',
                'api_server': 'Use SavedModel with confidence thresholding',
                'edge_device': 'Use TFLite quantized model'
            },
            'performance_expectations': {
                'accuracy_target': '>90% for high-confidence predictions',
                'inference_time': '<100ms on modern hardware',
                'confidence_threshold': pp_config.get('minimum_display', 0.7),
                'rejection_rate': '10-20% for quality control'
            }
        }
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = f"reports/deployment_report_{timestamp}.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"📋 Deployment report saved to {report_path}")
        return report

def main():
    """Main training pipeline execution"""
    parser = argparse.ArgumentParser(description='Complete Plant Recognition Training Pipeline')
    parser.add_argument('--config', type=str, default='config.json', help='Configuration file')
    parser.add_argument('--architecture', type=str, default='efficientnet_b3', 
                       choices=['mobilenet_large', 'mobilenet_small', 'efficientnet_b0', 
                               'efficientnet_b3', 'efficientnet_b7', 'resnet_50', 'resnet_101'])
    parser.add_argument('--train-dir', type=str, default='data/train', help='Training data directory')
    parser.add_argument('--val-dir', type=str, default='data/val', help='Validation data directory')
    parser.add_argument('--test-dir', type=str, default='data/test', help='Test data directory')
    parser.add_argument('--export-format', type=str, default='savedmodel', 
                       choices=['savedmodel', 'tflite', 'onnx'])
    
    args = parser.parse_args()
    
    # Load configuration
    config = {
        'architecture': args.architecture,
        'input_shape': [224, 224, 3],
        'batch_size': 32,
        'model_name': f'plant_recognition_{args.architecture}'
    }
    
    if os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config.update(json.load(f))
    
    # Initialize pipeline
    pipeline = CompletePlantRecognitionPipeline(config)
    
    # Create model
    pipeline.create_model(args.architecture)
    
    # Setup post-processing
    pipeline.setup_post_processing()
    
    # Train model (if data directories exist)
    if os.path.exists(args.train_dir) and os.path.exists(args.val_dir):
        training_history = pipeline.train_model(args.train_dir, args.val_dir)
        
        # Evaluate model (if test directory exists)
        if os.path.exists(args.test_dir):
            evaluation_results = pipeline.evaluate_model(args.test_dir)
        
        # Export model
        pipeline.export_model(args.export_format)
        
        # Generate deployment report
        deployment_report = pipeline.generate_deployment_report()
        
        logger.info("🎉 Complete pipeline executed successfully!")
    else:
        logger.warning("⚠️ Data directories not found. Model created but not trained.")
        logger.info("📁 Please organize your data in the following structure:")
        logger.info("   data/train/class1/image1.jpg")
        logger.info("   data/train/class1/image2.jpg")
        logger.info("   data/val/class1/image1.jpg")
        logger.info("   data/test/class1/image1.jpg")

if __name__ == "__main__":
    main()

# Example usage for demonstration
def demo_complete_solution():
    """Demonstrate the complete solution with sample data"""
    print("🌿 Medicinal Plant Recognition - Complete Solution Demo")
    print("=" * 60)

    # Configuration for demo
    demo_config = {
        'architecture': 'efficientnet_b3',
        'input_shape': [224, 224, 3],
        'batch_size': 16,
        'model_name': 'demo_plant_model'
    }

    # Initialize pipeline
    pipeline = CompletePlantRecognitionPipeline(demo_config)

    # Create model
    model, base_model = pipeline.create_model('efficientnet_b3')

    # Setup post-processing
    pipeline.setup_post_processing()

    # Simulate a prediction with dummy data
    dummy_image = np.random.rand(224, 224, 3)

    try:
        result = pipeline.predict_with_intelligence(dummy_image)

        print("\n🔍 Sample Prediction Result:")
        print(f"  Predicted Class: {result['prediction']['class']}")
        print(f"  Confidence: {result['prediction']['confidence']:.1%}")
        print(f"  Should Show: {result['prediction']['should_show']}")
        print(f"  Image Quality: {result['image_quality']:.2f}")
        print(f"  Recommendation: {result['recommendation'][:100]}...")

        if 'plant_info' in result:
            print(f"\n🌿 Plant Information Available:")
            print(f"  Scientific Name: {result['plant_info']['scientific_name']}")
            print(f"  Common Names: {', '.join(result['plant_info']['common_names'][:3])}")

    except Exception as e:
        print(f"⚠️ Demo prediction failed: {e}")

    # Generate deployment report
    try:
        report = pipeline.generate_deployment_report()
        print(f"\n📊 Model Statistics:")
        print(f"  Total Parameters: {report['model_info']['total_parameters']:,}")
        print(f"  Number of Classes: {report['model_info']['num_classes']}")
        print(f"  Knowledge Base Plants: {report['knowledge_base']['total_plants']}")
    except Exception as e:
        print(f"⚠️ Report generation failed: {e}")

    print("\n✅ Demo completed successfully!")
    print("\n📋 Next Steps:")
    print("1. Organize your plant images in data/train, data/val, data/test directories")
    print("2. Run: python complete_training_pipeline.py --architecture efficientnet_b3")
    print("3. Use the trained model for plant identification")
    print("4. Integrate with your web/mobile application")

# Uncomment to run demo
# demo_complete_solution()
