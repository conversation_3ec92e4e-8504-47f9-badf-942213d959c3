#!/usr/bin/env python3
"""
Simple test to verify the corrected plant database
"""

import json

def test_database():
    print("🌿 Testing Corrected Plant Database")
    print("=" * 50)
    
    try:
        # Load database
        with open('corrected_plant_database.json', 'r', encoding='utf-8') as f:
            db = json.load(f)
        
        print(f"✅ Database loaded successfully!")
        print(f"📊 Total plants: {len(db)}")
        
        # Test each plant
        for plant_key, plant_data in db.items():
            print(f"\n🌱 {plant_key.upper()}:")
            print(f"   Scientific: {plant_data['scientific_name']}")
            print(f"   Local: {plant_data['local_name']}")
            print(f"   Hindi: {plant_data.get('hindi_name', 'N/A')}")
            print(f"   Features: {len(plant_data['medicinal_features'])}")
            
            # Show first medicinal feature
            if plant_data['medicinal_features']:
                feature = plant_data['medicinal_features'][0]
                print(f"   Top feature: {feature['name']} ({feature['usage_frequency']})")
        
        print(f"\n✅ All plants verified successfully!")
        
        # Test specific plant (Aloe Vera)
        if 'aloe_vera' in db:
            aloe = db['aloe_vera']
            print(f"\n🔍 DETAILED TEST - ALOE VERA:")
            print(f"Scientific Name: {aloe['scientific_name']}")
            print(f"Local Name: {aloe['local_name']}")
            print(f"Hindi Name: {aloe['hindi_name']}")
            print(f"Tamil Name: {aloe['tamil_name']}")
            print(f"Telugu Name: {aloe['telugu_name']}")
            
            print(f"\nMedicinal Features:")
            for i, feature in enumerate(aloe['medicinal_features'], 1):
                print(f"{i}. {feature['name']} - {feature['description']}")
                print(f"   Preparation: {feature['preparation']}")
            
            print(f"\nDistinguishing Features:")
            for key, value in aloe['distinguishing_features'].items():
                print(f"• {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_database()
