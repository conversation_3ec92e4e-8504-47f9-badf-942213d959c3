#!/usr/bin/env python3
"""
🌿 Medicinal Plant Recognition - Advanced Training Strategy
Implements transfer learning, fine-tuning, and optimization for plant classification
"""

import tensorflow as tf
from tensorflow.keras import callbacks, optimizers, losses, metrics
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import numpy as np
from typing import Tuple, Dict, List, Optional
import os
import json
from datetime import datetime

class PlantTrainingStrategy:
    """Advanced training strategy for medicinal plant recognition"""
    
    def __init__(self, model, base_model, num_classes: int):
        self.model = model
        self.base_model = base_model
        self.num_classes = num_classes
        self.training_history = {}
        
    def create_data_generators(self, train_dir: str, val_dir: str, 
                             batch_size: int = 32, target_size: Tuple[int, int] = (224, 224)) -> Tuple:
        """Create optimized data generators with plant-specific augmentation"""
        
        # Training data generator with comprehensive augmentation
        train_datagen = ImageDataGenerator(
            rescale=1./255,
            rotation_range=30,              # Natural viewing angles
            width_shift_range=0.2,          # Position variations
            height_shift_range=0.2,         # Vertical shifts
            shear_range=0.1,               # Perspective changes
            zoom_range=[0.8, 1.2],         # Distance variations
            horizontal_flip=True,           # Mirror images
            brightness_range=[0.7, 1.3],   # Lighting conditions
            channel_shift_range=20,         # Color variations
            fill_mode='nearest',            # Fill missing pixels
            # Advanced augmentations
            preprocessing_function=self._advanced_augmentation
        )
        
        # Validation data generator (no augmentation)
        val_datagen = ImageDataGenerator(
            rescale=1./255,
            preprocessing_function=self._preprocessing_function
        )
        
        # Create generators
        train_generator = train_datagen.flow_from_directory(
            train_dir,
            target_size=target_size,
            batch_size=batch_size,
            class_mode='categorical',
            shuffle=True,
            seed=42
        )
        
        val_generator = val_datagen.flow_from_directory(
            val_dir,
            target_size=target_size,
            batch_size=batch_size,
            class_mode='categorical',
            shuffle=False,
            seed=42
        )
        
        return train_generator, val_generator
    
    def _advanced_augmentation(self, image):
        """Apply advanced augmentation techniques for plant images"""
        # Convert to float32
        image = tf.cast(image, tf.float32)
        
        # Random gaussian blur (simulate camera focus issues)
        if tf.random.uniform([]) < 0.3:
            image = tf.image.gaussian_blur(image, sigma=tf.random.uniform([], 0.1, 1.0))
        
        # Random noise injection (simulate sensor noise)
        if tf.random.uniform([]) < 0.2:
            noise = tf.random.normal(tf.shape(image), stddev=0.02)
            image = tf.clip_by_value(image + noise, 0.0, 1.0)
        
        # Random saturation adjustment (natural color variations)
        image = tf.image.random_saturation(image, 0.7, 1.3)
        
        # Random hue adjustment (lighting color temperature)
        image = tf.image.random_hue(image, 0.1)
        
        return image
    
    def _preprocessing_function(self, image):
        """Standard preprocessing for validation/test images"""
        image = tf.cast(image, tf.float32)
        # Apply any consistent preprocessing here
        return image
    
    def create_callbacks(self, model_name: str, patience: int = 10) -> List:
        """Create comprehensive callbacks for training optimization"""
        
        # Create directories
        os.makedirs(f'models/{model_name}', exist_ok=True)
        os.makedirs(f'logs/{model_name}', exist_ok=True)
        
        callbacks_list = [
            # Early stopping with patience
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=patience,
                restore_best_weights=True,
                verbose=1,
                mode='max'
            ),
            
            # Model checkpointing
            callbacks.ModelCheckpoint(
                filepath=f'models/{model_name}/best_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                verbose=1,
                mode='max'
            ),
            
            # Learning rate reduction
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1,
                mode='min'
            ),
            
            # TensorBoard logging
            callbacks.TensorBoard(
                log_dir=f'logs/{model_name}/{datetime.now().strftime("%Y%m%d-%H%M%S")}',
                histogram_freq=1,
                write_graph=True,
                write_images=True,
                update_freq='epoch'
            ),
            
            # Custom callback for confidence analysis
            ConfidenceAnalysisCallback(self.num_classes),
            
            # Learning rate scheduler
            callbacks.LearningRateScheduler(self._lr_schedule, verbose=1)
        ]
        
        return callbacks_list
    
    def _lr_schedule(self, epoch, lr):
        """Custom learning rate schedule"""
        if epoch < 10:
            return lr
        elif epoch < 20:
            return lr * 0.5
        elif epoch < 30:
            return lr * 0.1
        else:
            return lr * 0.01
    
    def phase_1_training(self, train_gen, val_gen, epochs: int = 20) -> Dict:
        """Phase 1: Train only the classifier head (frozen base model)"""
        print("🚀 Phase 1: Training classifier head with frozen base model...")
        
        # Ensure base model is frozen
        self.base_model.trainable = False
        
        # Compile with higher learning rate for new layers
        self.model.compile(
            optimizer=optimizers.Adam(learning_rate=1e-3),
            loss=losses.CategoricalCrossentropy(label_smoothing=0.1),
            metrics=['accuracy', 'top_3_accuracy']
        )
        
        # Train
        history = self.model.fit(
            train_gen,
            epochs=epochs,
            validation_data=val_gen,
            callbacks=self.create_callbacks('phase1_frozen', patience=8),
            verbose=1
        )
        
        self.training_history['phase1'] = history.history
        return history.history
    
    def phase_2_training(self, train_gen, val_gen, epochs: int = 30, 
                        unfreeze_layers: int = -50) -> Dict:
        """Phase 2: Fine-tune the entire model with lower learning rate"""
        print("🔥 Phase 2: Fine-tuning entire model...")
        
        # Unfreeze top layers of base model
        self.base_model.trainable = True
        
        # Freeze bottom layers, unfreeze top layers
        for layer in self.base_model.layers[:unfreeze_layers]:
            layer.trainable = False
        
        print(f"Unfrozen layers: {sum(1 for layer in self.base_model.layers if layer.trainable)}")
        
        # Compile with lower learning rate for fine-tuning
        self.model.compile(
            optimizer=optimizers.Adam(learning_rate=1e-5),
            loss=losses.CategoricalCrossentropy(label_smoothing=0.1),
            metrics=['accuracy', 'top_3_accuracy']
        )
        
        # Train
        history = self.model.fit(
            train_gen,
            epochs=epochs,
            validation_data=val_gen,
            callbacks=self.create_callbacks('phase2_finetuned', patience=12),
            verbose=1
        )
        
        self.training_history['phase2'] = history.history
        return history.history
    
    def phase_3_training(self, train_gen, val_gen, epochs: int = 20) -> Dict:
        """Phase 3: Final optimization with very low learning rate"""
        print("✨ Phase 3: Final optimization...")
        
        # All layers trainable
        self.base_model.trainable = True
        
        # Compile with very low learning rate
        self.model.compile(
            optimizer=optimizers.Adam(learning_rate=1e-6),
            loss=losses.CategoricalCrossentropy(label_smoothing=0.05),
            metrics=['accuracy', 'top_3_accuracy']
        )
        
        # Train
        history = self.model.fit(
            train_gen,
            epochs=epochs,
            validation_data=val_gen,
            callbacks=self.create_callbacks('phase3_optimized', patience=15),
            verbose=1
        )
        
        self.training_history['phase3'] = history.history
        return history.history
    
    def full_training_pipeline(self, train_dir: str, val_dir: str, 
                             batch_size: int = 32) -> Dict:
        """Execute complete 3-phase training pipeline"""
        print("🌿 Starting Complete Plant Recognition Training Pipeline...")
        
        # Create data generators
        train_gen, val_gen = self.create_data_generators(
            train_dir, val_dir, batch_size
        )
        
        # Execute 3-phase training
        phase1_history = self.phase_1_training(train_gen, val_gen, epochs=20)
        phase2_history = self.phase_2_training(train_gen, val_gen, epochs=30)
        phase3_history = self.phase_3_training(train_gen, val_gen, epochs=20)
        
        # Save complete training history
        with open('training_history.json', 'w') as f:
            json.dump(self.training_history, f, indent=2)
        
        # Save final model
        self.model.save('final_plant_recognition_model.h5')
        
        print("✅ Training pipeline completed successfully!")
        return self.training_history

class ConfidenceAnalysisCallback(callbacks.Callback):
    """Custom callback to analyze prediction confidence during training"""
    
    def __init__(self, num_classes):
        super().__init__()
        self.num_classes = num_classes
        
    def on_epoch_end(self, epoch, logs=None):
        """Analyze confidence distribution at end of each epoch"""
        if hasattr(self.validation_data, '__len__'):
            # Get predictions on validation data
            val_predictions = self.model.predict(self.validation_data, verbose=0)
            
            # Calculate confidence statistics
            max_confidences = np.max(val_predictions, axis=1)
            mean_confidence = np.mean(max_confidences)
            high_confidence_ratio = np.mean(max_confidences > 0.8)
            
            print(f"\n📊 Confidence Analysis - Epoch {epoch + 1}:")
            print(f"   Mean Confidence: {mean_confidence:.3f}")
            print(f"   High Confidence (>80%): {high_confidence_ratio:.3f}")
            
            # Log to TensorBoard
            logs = logs or {}
            logs['mean_confidence'] = mean_confidence
            logs['high_confidence_ratio'] = high_confidence_ratio

# Advanced training utilities
class TrainingUtils:
    """Utility functions for training optimization"""
    
    @staticmethod
    def calculate_class_weights(train_generator) -> Dict:
        """Calculate class weights for imbalanced datasets"""
        from sklearn.utils.class_weight import compute_class_weight
        
        # Get class distribution
        class_counts = {}
        for class_name in train_generator.class_indices:
            class_idx = train_generator.class_indices[class_name]
            class_counts[class_idx] = len(train_generator.filepaths[
                train_generator.labels == class_idx
            ])
        
        # Calculate weights
        classes = list(class_counts.keys())
        counts = list(class_counts.values())
        
        weights = compute_class_weight(
            'balanced',
            classes=np.array(classes),
            y=np.repeat(classes, counts)
        )
        
        return dict(zip(classes, weights))
    
    @staticmethod
    def create_confusion_matrix_callback(val_generator, class_names):
        """Create callback to generate confusion matrix"""
        # Implementation for confusion matrix generation
        pass

# Example usage
if __name__ == "__main__":
    from model_architectures import PlantRecognitionModels
    
    # Create model
    num_classes = 31
    model_factory = PlantRecognitionModels(num_classes)
    model, base_model = model_factory.create_efficientnet('B3')
    
    # Initialize training strategy
    trainer = PlantTrainingStrategy(model, base_model, num_classes)
    
    # Execute training pipeline
    # trainer.full_training_pipeline('data/train', 'data/val')
    
    print("🌿 Training strategy initialized successfully!")
