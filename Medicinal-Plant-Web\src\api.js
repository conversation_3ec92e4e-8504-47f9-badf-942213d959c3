const BACKEND = import.meta.env.VITE_BACKEND_URL || ''

function timeout(ms) {
  return new Promise((_, rej) => setTimeout(() => rej(new Error('Request timed out')), ms))
}

export async function predictImage(file) {
  const form = new FormData()
  form.append('image', file)

  const url = (BACKEND ? BACKEND.replace(/\/$/, '') : '') + '/api/predict'

  try {
    const res = await Promise.race([
      fetch(url, { method: 'POST', body: form }),
      timeout(30000)
    ])

    if (res.status === 503) {
      // Backend says model not loaded — fall back to a client-side mock response
      return {
        label: 'mock_plant',
        confidence: 0.78,
        scientificName: 'Exampleus plantus',
        localName: 'Example Plant',
        medicinalFeature: ['anti-inflammatory', 'antioxidant'],
        _mock: true
      }
    }

    if (!res.ok) {
      const text = await res.text()
      throw new Error(text || res.statusText)
    }

    return res.json()
  } catch (err) {
    // network or timeout — provide a friendly error or fallback mock
    if (err && /timed out/i.test(String(err))) {
      throw new Error('Request timed out')
    }
    // final fallback: return a mock prediction so UI can continue to be tested offline
    return {
      label: 'mock_plant',
      confidence: 0.6,
      scientificName: 'Fallbackus testus',
      localName: 'Fallback Plant',
      medicinalFeature: ['unknown'],
      _mock: true,
      _error: String(err)
    }
  }
}

export async function saveRecord(payload) {
  const url = (BACKEND ? BACKEND.replace(/\/$/, '') : '') + '/api/save'
  const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) })
  if (!res.ok) {
    const text = await res.text()
    throw new Error(text || res.statusText)
  }
  return res.json()
}

export async function sendFeedback(payload) {
  const url = (BACKEND ? BACKEND.replace(/\/$/, '') : '') + '/api/feedback'
  const res = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) })
  if (!res.ok) {
    const text = await res.text()
    throw new Error(text || res.statusText)
  }
  return res.json()
}
