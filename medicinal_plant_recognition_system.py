#!/usr/bin/env python3
"""
🌿 Medicinal Plant Recognition System
Comprehensive plant identification with traditional medicine information
Following the structured output format: Overview, Traditional Use, Preparation, Safety, Geography
"""

import numpy as np
import tensorflow as tf
from typing import Dict, List, Optional, Tuple
import json
from datetime import datetime
import cv2
from PIL import Image

# Import our enhanced modules
from complete_training_pipeline import CompletePlantRecognitionPipeline
from enhanced_knowledge_base import EnhancedKnowledgeBase
from post_processing import PlantPostProcessor

class MedicinalPlantRecognitionSystem:
    """
    Complete medicinal plant recognition system following the specified prompt structure
    """
    
    def __init__(self, model_path: Optional[str] = None):
        self.model = None
        self.pipeline = None
        self.knowledge_base = EnhancedKnowledgeBase()
        self.confidence_threshold = 70  # As specified in prompt
        
        # Enhanced plant database with traditional medicine systems
        self.enhanced_plant_db = self._load_enhanced_plant_database()
        
        if model_path:
            self._load_model(model_path)
        else:
            self._initialize_demo_system()
    
    def _load_model(self, model_path: str):
        """Load trained model"""
        try:
            self.model = tf.keras.models.load_model(model_path)
            print(f"✅ Model loaded from {model_path}")
        except Exception as e:
            print(f"⚠️ Could not load model: {e}")
            self._initialize_demo_system()
    
    def _initialize_demo_system(self):
        """Initialize demo system with mock predictions"""
        config = {
            'architecture': 'efficientnet_b3',
            'input_shape': [224, 224, 3],
            'model_name': 'medicinal_plant_demo'
        }
        
        self.pipeline = CompletePlantRecognitionPipeline(config)
        print("🌿 Demo system initialized")
    
    def _load_enhanced_plant_database(self) -> Dict:
        """Load comprehensive plant database with traditional medicine info"""
        return {
            "Ocimum_tenuiflorum": {
                "common_name": "Holy Basil",
                "local_names": {
                    "hindi": "Tulsi",
                    "tamil": "Thulasi", 
                    "telugu": "Tulasi",
                    "sanskrit": "Surasa",
                    "bengali": "Tulsi",
                    "marathi": "Tulas"
                },
                "scientific_name": "Ocimum tenuiflorum",
                "family": "Lamiaceae",
                "traditional_systems": {
                    "ayurveda": {
                        "uses": ["Fever", "Cough", "Cold", "Immunity booster", "Stress relief", "Respiratory disorders"],
                        "properties": "Pungent, bitter, heating, light",
                        "doshas": "Balances Kapha and Vata"
                    },
                    "siddha": {
                        "uses": ["Respiratory diseases", "Skin infections", "Digestion", "Fever", "Cough"],
                        "properties": "Hot potency, pungent taste"
                    },
                    "unani": {
                        "uses": ["Anti-inflammatory", "Fever", "Antimicrobial", "Digestive disorders"],
                        "properties": "Hot and dry temperament"
                    }
                },
                "modern_benefits": [
                    "Adaptogenic properties", "Immune system support", "Stress reduction",
                    "Antioxidant effects", "Anti-inflammatory", "Antimicrobial"
                ],
                "preparation_methods": [
                    {"method": "Tea", "preparation": "Boil 5-7 fresh leaves in water for 5 minutes"},
                    {"method": "Juice", "preparation": "Extract fresh leaf juice, mix with honey"},
                    {"method": "Powder", "preparation": "Dry leaves and grind to powder"},
                    {"method": "Paste", "preparation": "Crush fresh leaves with water"}
                ],
                "safety_notes": [
                    "Avoid excess use during pregnancy",
                    "May lower blood sugar levels",
                    "Can interact with blood-thinning medications",
                    "Generally safe for most people in moderate amounts"
                ],
                "geography": {
                    "native_region": "Indian subcontinent",
                    "habitat": "Tropical and subtropical regions",
                    "growing_conditions": "Well-drained soil, partial shade to full sun",
                    "cultivation": "Widely cultivated in India, Southeast Asia"
                },
                "distinguishing_features": {
                    "leaves": "Oval, serrated edges, aromatic",
                    "smell": "Strong, clove-like fragrance",
                    "flowers": "Small, purple or white spikes",
                    "stem": "Square-shaped, hairy"
                }
            },
            
            "Andrographis_paniculata": {
                "common_name": "King of Bitters",
                "local_names": {
                    "hindi": "Kalmegh",
                    "tamil": "Nilavembu",
                    "telugu": "Nelavemu",
                    "bengali": "Kalmegh",
                    "gujarati": "Kariyatu"
                },
                "scientific_name": "Andrographis paniculata",
                "family": "Acanthaceae",
                "traditional_systems": {
                    "ayurveda": {
                        "uses": ["Fever", "Liver disorders", "Digestive problems", "Immune enhancement"],
                        "properties": "Bitter, cold, light, dry",
                        "doshas": "Reduces Pitta and Kapha"
                    },
                    "siddha": {
                        "uses": ["Fever", "Malaria", "Liver protection", "Digestive disorders"],
                        "properties": "Bitter taste, cold potency"
                    },
                    "unani": {
                        "uses": ["Fever", "Liver complaints", "Digestive disorders", "Blood purification"],
                        "properties": "Cold and dry temperament"
                    }
                },
                "modern_benefits": [
                    "Hepatoprotective", "Immunomodulatory", "Anti-inflammatory",
                    "Antimalarial", "Antiviral", "Antioxidant"
                ],
                "preparation_methods": [
                    {"method": "Decoction", "preparation": "Boil 3-5g dried herb in 200ml water"},
                    {"method": "Powder", "preparation": "1-3g powder with honey or water"},
                    {"method": "Juice", "preparation": "Fresh leaf juice 5-10ml twice daily"},
                    {"method": "Tea", "preparation": "Steep dried leaves in hot water for 10 minutes"}
                ],
                "safety_notes": [
                    "Very bitter taste - start with small doses",
                    "Avoid during pregnancy and breastfeeding",
                    "May cause stomach upset in sensitive individuals",
                    "Can interact with blood pressure medications"
                ],
                "geography": {
                    "native_region": "India and Sri Lanka",
                    "habitat": "Plains, hillsides, coastal areas",
                    "growing_conditions": "Moist, well-drained soil",
                    "cultivation": "Cultivated throughout India, China, Thailand"
                },
                "distinguishing_features": {
                    "leaves": "Lance-shaped, smooth edges, very bitter",
                    "flowers": "Small, white with purple spots",
                    "stem": "Square, branched",
                    "taste": "Extremely bitter"
                }
            },
            
            "Plectranthus_amboinicus": {
                "common_name": "Indian Borage",
                "local_names": {
                    "hindi": "Ajwain Patta",
                    "tamil": "Karpooravalli",
                    "telugu": "Vamu Aku",
                    "bengali": "Patta Ajwain",
                    "marathi": "Pandhara Nirgudi"
                },
                "scientific_name": "Plectranthus amboinicus",
                "family": "Lamiaceae",
                "traditional_systems": {
                    "ayurveda": {
                        "uses": ["Cough", "Cold", "Digestive disorders", "Skin problems"],
                        "properties": "Pungent, warm, light, penetrating",
                        "doshas": "Reduces Kapha and Vata"
                    },
                    "siddha": {
                        "uses": ["Respiratory ailments", "Stomach pain", "Skin diseases", "Fever"],
                        "properties": "Heating, aromatic, carminative"
                    },
                    "unani": {
                        "uses": ["Bronchitis", "Asthma", "Digestive problems", "Wounds"],
                        "properties": "Hot and dry temperament"
                    }
                },
                "modern_benefits": [
                    "Antimicrobial", "Anti-inflammatory", "Expectorant",
                    "Carminative", "Antispasmodic", "Wound healing"
                ],
                "preparation_methods": [
                    {"method": "Juice", "preparation": "Crush fresh leaves, extract juice with honey"},
                    {"method": "Decoction", "preparation": "Boil 5-7 leaves in water for 5 minutes"},
                    {"method": "Poultice", "preparation": "Crush fresh leaves for external application"},
                    {"method": "Steam inhalation", "preparation": "Add leaves to hot water, inhale steam"}
                ],
                "safety_notes": [
                    "Generally safe for most people",
                    "May cause skin irritation in sensitive individuals",
                    "Avoid large amounts during pregnancy",
                    "Test for skin sensitivity before topical use"
                ],
                "geography": {
                    "native_region": "Southern and Eastern Africa",
                    "habitat": "Tropical and subtropical regions",
                    "growing_conditions": "Warm, humid conditions",
                    "cultivation": "Widely cultivated in India, Southeast Asia, Caribbean"
                },
                "distinguishing_features": {
                    "leaves": "Thick, fleshy, serrated edges, hairy surface",
                    "smell": "Strong oregano-like aroma",
                    "texture": "Succulent, aromatic when crushed",
                    "difference_from_tulsi": "Thicker leaves, different aroma, more fleshy"
                }
            }
        }
    
    def identify_plant(self, image_input, confidence_threshold: float = 70) -> Dict:
        """
        Main plant identification function following the specified prompt structure
        
        Args:
            image_input: Image file path, numpy array, or PIL Image
            confidence_threshold: Minimum confidence to show single result
            
        Returns:
            Structured plant information following the prompt format
        """
        
        # Process image
        processed_image = self._preprocess_image(image_input)
        
        # Get prediction (mock for demo, replace with actual model prediction)
        prediction_result = self._get_prediction(processed_image)
        
        # Format result according to prompt structure
        return self._format_result(prediction_result, confidence_threshold)
    
    def _preprocess_image(self, image_input) -> np.ndarray:
        """Preprocess image for model input"""
        if isinstance(image_input, str):
            # Load from file path
            image = cv2.imread(image_input)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        elif isinstance(image_input, np.ndarray):
            image = image_input
        elif hasattr(image_input, 'convert'):  # PIL Image
            image = np.array(image_input.convert('RGB'))
        else:
            raise ValueError("Unsupported image input type")
        
        # Resize to model input size
        image = cv2.resize(image, (224, 224))
        image = image.astype(np.float32) / 255.0
        
        return image
    
    def _get_prediction(self, image: np.ndarray) -> Dict:
        """Get model prediction (mock implementation for demo)"""
        
        # Mock predictions for demo - replace with actual model inference
        mock_predictions = {
            "Ocimum_tenuiflorum": 0.94,
            "Andrographis_paniculata": 0.03,
            "Plectranthus_amboinicus": 0.02,
            "Azadirachta_indica": 0.01
        }
        
        # Sort by confidence
        sorted_predictions = sorted(mock_predictions.items(), key=lambda x: x[1], reverse=True)
        
        return {
            "top_prediction": sorted_predictions[0],
            "all_predictions": sorted_predictions,
            "confidence": sorted_predictions[0][1] * 100
        }
    
    def _format_result(self, prediction_result: Dict, confidence_threshold: float) -> Dict:
        """Format result according to the specified prompt structure"""
        
        top_class, top_confidence = prediction_result["top_prediction"]
        confidence_percent = prediction_result["confidence"]
        
        # Check if confidence meets threshold
        if confidence_percent < confidence_threshold:
            return self._format_low_confidence_result(prediction_result, confidence_threshold)
        
        # Get plant information
        plant_info = self.enhanced_plant_db.get(top_class)
        if not plant_info:
            return self._format_unknown_plant_result(top_class, confidence_percent)
        
        # Format according to prompt structure
        result = {
            "identification": {
                "common_name": plant_info["common_name"],
                "scientific_name": plant_info["scientific_name"],
                "local_names": plant_info["local_names"],
                "family": plant_info["family"],
                "confidence": f"{confidence_percent:.0f}%",
                "emoji": "🌿"
            },
            
            "overview": {
                "description": f"{plant_info['common_name']} ({plant_info['scientific_name']}) is a medicinal plant from the {plant_info['family']} family.",
                "modern_benefits": plant_info["modern_benefits"]
            },
            
            "traditional_use": {
                "ayurveda": {
                    "uses": plant_info["traditional_systems"]["ayurveda"]["uses"],
                    "properties": plant_info["traditional_systems"]["ayurveda"]["properties"]
                },
                "siddha": {
                    "uses": plant_info["traditional_systems"]["siddha"]["uses"],
                    "properties": plant_info["traditional_systems"]["siddha"]["properties"]
                },
                "unani": {
                    "uses": plant_info["traditional_systems"]["unani"]["uses"],
                    "properties": plant_info["traditional_systems"]["unani"]["properties"]
                }
            },
            
            "preparation": {
                "methods": plant_info["preparation_methods"]
            },
            
            "safety": {
                "caution_notes": plant_info["safety_notes"],
                "general_advice": "Consult healthcare provider before medicinal use"
            },
            
            "geography": {
                "native_region": plant_info["geography"]["native_region"],
                "habitat": plant_info["geography"]["habitat"],
                "cultivation": plant_info["geography"]["cultivation"]
            },
            
            "distinguishing_features": plant_info["distinguishing_features"]
        }
        
        return result
    
    def _format_low_confidence_result(self, prediction_result: Dict, threshold: float) -> Dict:
        """Format result when confidence is below threshold - show top 3 matches"""
        
        top_3_predictions = prediction_result["all_predictions"][:3]
        
        result = {
            "identification": {
                "status": "Low Confidence",
                "confidence_threshold": f"{threshold}%",
                "message": f"Confidence below {threshold}%. Showing top 3 possible matches:",
                "emoji": "❓"
            },
            
            "top_3_matches": []
        }
        
        for i, (plant_class, confidence) in enumerate(top_3_predictions, 1):
            plant_info = self.enhanced_plant_db.get(plant_class)
            if plant_info:
                match_info = {
                    "rank": i,
                    "common_name": plant_info["common_name"],
                    "scientific_name": plant_info["scientific_name"],
                    "confidence": f"{confidence * 100:.1f}%",
                    "key_uses": plant_info["traditional_systems"]["ayurveda"]["uses"][:3],
                    "distinguishing_features": plant_info["distinguishing_features"]
                }
                result["top_3_matches"].append(match_info)
        
        result["recommendation"] = {
            "advice": "Please take a clearer photo focusing on leaves, flowers, or distinctive features",
            "tips": [
                "Ensure good lighting",
                "Focus on leaf shape and arrangement",
                "Include flowers if present",
                "Take multiple angles"
            ]
        }
        
        return result
    
    def _format_unknown_plant_result(self, plant_class: str, confidence: float) -> Dict:
        """Format result for unknown plants"""
        return {
            "identification": {
                "status": "Unknown Plant",
                "detected_class": plant_class,
                "confidence": f"{confidence:.1f}%",
                "message": "Plant not found in medicinal database",
                "emoji": "❓"
            },
            "recommendation": {
                "advice": "This plant is not in our medicinal plant database",
                "suggestion": "Please consult a botanist or plant expert for identification"
            }
        }
    
    def display_formatted_result(self, result: Dict) -> str:
        """Display result in the specified format"""
        
        if "top_3_matches" in result:
            return self._display_low_confidence_result(result)
        
        if result["identification"].get("status") == "Unknown Plant":
            return self._display_unknown_result(result)
        
        # Standard high-confidence result
        identification = result["identification"]
        overview = result["overview"]
        traditional = result["traditional_use"]
        preparation = result["preparation"]
        safety = result["safety"]
        geography = result["geography"]
        features = result["distinguishing_features"]
        
        output = f"""
{identification['common_name']} {identification['emoji']}

Scientific Name: {identification['scientific_name']}

Local Names: {', '.join([f"{name.title()}: {local}" for name, local in identification['local_names'].items()])}

Confidence: {identification['confidence']}

📋 OVERVIEW:
{overview['description']}

Modern Benefits: {', '.join(overview['modern_benefits'])}

🌿 TRADITIONAL USE:

Ayurveda:
Uses: {', '.join(traditional['ayurveda']['uses'])}
Properties: {traditional['ayurveda']['properties']}

Siddha:
Uses: {', '.join(traditional['siddha']['uses'])}
Properties: {traditional['siddha']['properties']}

Unani:
Uses: {', '.join(traditional['unani']['uses'])}
Properties: {traditional['unani']['properties']}

🧪 PREPARATION:
"""
        
        for method in preparation['methods']:
            output += f"• {method['method']}: {method['preparation']}\n"
        
        output += f"""
⚠️ SAFETY:
"""
        for note in safety['caution_notes']:
            output += f"• {note}\n"
        
        output += f"• {safety['general_advice']}\n"
        
        output += f"""
🌍 GEOGRAPHY:
Native Region: {geography['native_region']}
Habitat: {geography['habitat']}
Cultivation: {geography['cultivation']}

🔍 DISTINGUISHING FEATURES:
"""
        for feature, description in features.items():
            if feature != 'difference_from_tulsi':  # Skip comparison features in main display
                output += f"• {feature.title()}: {description}\n"
        
        return output
    
    def _display_low_confidence_result(self, result: Dict) -> str:
        """Display low confidence result with top 3 matches"""
        
        output = f"""
{result['identification']['emoji']} {result['identification']['status']}

{result['identification']['message']}

TOP 3 POSSIBLE MATCHES:

"""
        
        for match in result['top_3_matches']:
            output += f"""
{match['rank']}. {match['common_name']}
   Scientific Name: {match['scientific_name']}
   Confidence: {match['confidence']}
   Key Uses: {', '.join(match['key_uses'])}
   Features: {', '.join([f"{k}: {v}" for k, v in match['distinguishing_features'].items()])}

"""
        
        output += f"""
💡 RECOMMENDATION:
{result['recommendation']['advice']}

Tips for better identification:
"""
        for tip in result['recommendation']['tips']:
            output += f"• {tip}\n"
        
        return output
    
    def _display_unknown_result(self, result: Dict) -> str:
        """Display unknown plant result"""
        identification = result["identification"]
        recommendation = result["recommendation"]
        
        return f"""
{identification['emoji']} {identification['status']}

Detected Class: {identification['detected_class']}
Confidence: {identification['confidence']}

{identification['message']}

💡 RECOMMENDATION:
{recommendation['advice']}
{recommendation['suggestion']}
"""

# Example usage and testing
if __name__ == "__main__":
    # Initialize the system
    system = MedicinalPlantRecognitionSystem()
    
    print("🌿 Medicinal Plant Recognition System Initialized")
    print("=" * 60)
    
    # Demo with mock image (replace with actual image path)
    mock_image = np.random.rand(224, 224, 3)
    
    # Test high confidence identification
    print("Testing high confidence identification:")
    result = system.identify_plant(mock_image, confidence_threshold=70)
    formatted_output = system.display_formatted_result(result)
    print(formatted_output)
    
    print("\n" + "="*60)
    
    # Test low confidence scenario
    print("Testing low confidence scenario:")
    result_low = system.identify_plant(mock_image, confidence_threshold=95)  # High threshold to trigger low confidence
    formatted_output_low = system.display_formatted_result(result_low)
    print(formatted_output_low)
