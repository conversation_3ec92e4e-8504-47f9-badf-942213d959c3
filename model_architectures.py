#!/usr/bin/env python3
"""
🌿 Medicinal Plant Recognition - Model Architecture Comparison
Implements and compares MobileNetV3, EfficientNet, and ResNet for plant classification
"""

import tensorflow as tf
from tensorflow.keras import layers, Model, optimizers
from tensorflow.keras.applications import (
    MobileNetV3Large, MobileNetV3Small,
    EfficientNetB0, EfficientNetB3, EfficientNetB7,
    ResNet50, ResNet101, ResNet152V2
)
import numpy as np
from typing import Tuple, Dict, List

class PlantRecognitionModels:
    """Factory class for creating optimized plant recognition models"""
    
    def __init__(self, num_classes: int, input_shape: Tuple[int, int, int] = (224, 224, 3)):
        self.num_classes = num_classes
        self.input_shape = input_shape
        
    def create_mobilenet_v3(self, variant: str = 'large', alpha: float = 1.0) -> Model:
        """
        Create MobileNetV3 model optimized for plant recognition
        
        Pros: Fast inference, mobile-friendly, good for real-time apps
        Cons: Lower accuracy than larger models
        Best for: Mobile deployment, real-time recognition
        """
        if variant == 'large':
            base_model = MobileNetV3Large(
                input_shape=self.input_shape,
                alpha=alpha,
                weights='imagenet',
                include_top=False,
                pooling='avg'
            )
        else:
            base_model = MobileNetV3Small(
                input_shape=self.input_shape,
                alpha=alpha,
                weights='imagenet',
                include_top=False,
                pooling='avg'
            )
        
        # Freeze base model initially
        base_model.trainable = False
        
        # Add custom classification head
        inputs = tf.keras.Input(shape=self.input_shape)
        x = base_model(inputs, training=False)
        
        # Plant-specific feature extraction
        x = layers.Dropout(0.3)(x)
        x = layers.Dense(512, activation='relu', name='plant_features')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.2)(x)
        
        # Classification layers
        x = layers.Dense(256, activation='relu')(x)
        x = layers.Dropout(0.1)(x)
        outputs = layers.Dense(self.num_classes, activation='softmax', name='predictions')(x)
        
        model = Model(inputs, outputs)
        return model, base_model
    
    def create_efficientnet(self, variant: str = 'B3') -> Model:
        """
        Create EfficientNet model optimized for plant recognition
        
        Pros: Best accuracy-to-size ratio, compound scaling
        Cons: Slower than MobileNet, more memory usage
        Best for: High accuracy requirements, server deployment
        """
        # Select EfficientNet variant
        if variant == 'B0':
            base_model = EfficientNetB0(
                input_shape=self.input_shape,
                weights='imagenet',
                include_top=False,
                pooling='avg'
            )
        elif variant == 'B3':
            base_model = EfficientNetB3(
                input_shape=self.input_shape,
                weights='imagenet',
                include_top=False,
                pooling='avg'
            )
        elif variant == 'B7':
            base_model = EfficientNetB7(
                input_shape=self.input_shape,
                weights='imagenet',
                include_top=False,
                pooling='avg'
            )
        
        # Freeze base model initially
        base_model.trainable = False
        
        # Add custom classification head
        inputs = tf.keras.Input(shape=self.input_shape)
        x = base_model(inputs, training=False)
        
        # Enhanced feature extraction for plant characteristics
        x = layers.Dropout(0.4)(x)
        x = layers.Dense(1024, activation='relu', name='plant_features_1')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.3)(x)
        
        x = layers.Dense(512, activation='relu', name='plant_features_2')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.2)(x)
        
        # Final classification
        outputs = layers.Dense(self.num_classes, activation='softmax', name='predictions')(x)
        
        model = Model(inputs, outputs)
        return model, base_model
    
    def create_resnet(self, variant: str = '50') -> Model:
        """
        Create ResNet model optimized for plant recognition
        
        Pros: Deep feature learning, proven architecture
        Cons: Large model size, slower inference
        Best for: High accuracy, complex plant distinctions
        """
        # Select ResNet variant
        if variant == '50':
            base_model = ResNet50(
                input_shape=self.input_shape,
                weights='imagenet',
                include_top=False,
                pooling='avg'
            )
        elif variant == '101':
            base_model = ResNet101(
                input_shape=self.input_shape,
                weights='imagenet',
                include_top=False,
                pooling='avg'
            )
        elif variant == '152':
            base_model = ResNet152V2(
                input_shape=self.input_shape,
                weights='imagenet',
                include_top=False,
                pooling='avg'
            )
        
        # Freeze base model initially
        base_model.trainable = False
        
        # Add custom classification head
        inputs = tf.keras.Input(shape=self.input_shape)
        x = base_model(inputs, training=False)
        
        # Deep feature extraction for complex plant patterns
        x = layers.Dropout(0.5)(x)
        x = layers.Dense(2048, activation='relu', name='deep_features_1')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.4)(x)
        
        x = layers.Dense(1024, activation='relu', name='deep_features_2')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.3)(x)
        
        x = layers.Dense(512, activation='relu', name='deep_features_3')(x)
        x = layers.Dropout(0.2)(x)
        
        # Final classification
        outputs = layers.Dense(self.num_classes, activation='softmax', name='predictions')(x)
        
        model = Model(inputs, outputs)
        return model, base_model

class ModelComparison:
    """Compare different model architectures for plant recognition"""
    
    @staticmethod
    def get_model_specs() -> Dict:
        """Get specifications for each model architecture"""
        return {
            'MobileNetV3-Large': {
                'params': '5.4M',
                'inference_speed': 'Very Fast (20ms)',
                'accuracy_potential': 'Good (85-90%)',
                'memory_usage': 'Low (22MB)',
                'best_for': 'Mobile apps, real-time recognition',
                'pros': ['Fast inference', 'Small size', 'Mobile optimized'],
                'cons': ['Lower accuracy', 'Limited feature depth']
            },
            'MobileNetV3-Small': {
                'params': '2.9M',
                'inference_speed': 'Fastest (10ms)',
                'accuracy_potential': 'Moderate (80-85%)',
                'memory_usage': 'Very Low (12MB)',
                'best_for': 'Resource-constrained devices',
                'pros': ['Minimal resources', 'Ultra-fast'],
                'cons': ['Lowest accuracy', 'Simple features only']
            },
            'EfficientNet-B0': {
                'params': '5.3M',
                'inference_speed': 'Fast (30ms)',
                'accuracy_potential': 'Very Good (88-92%)',
                'memory_usage': 'Low (21MB)',
                'best_for': 'Balanced performance',
                'pros': ['Great accuracy/size ratio', 'Efficient scaling'],
                'cons': ['Slightly slower than MobileNet']
            },
            'EfficientNet-B3': {
                'params': '12M',
                'inference_speed': 'Medium (60ms)',
                'accuracy_potential': 'Excellent (90-95%)',
                'memory_usage': 'Medium (48MB)',
                'best_for': 'High accuracy requirements',
                'pros': ['High accuracy', 'Good feature learning'],
                'cons': ['Larger size', 'More compute needed']
            },
            'EfficientNet-B7': {
                'params': '66M',
                'inference_speed': 'Slow (200ms)',
                'accuracy_potential': 'Outstanding (92-97%)',
                'memory_usage': 'High (264MB)',
                'best_for': 'Maximum accuracy, server deployment',
                'pros': ['Highest accuracy', 'Complex pattern recognition'],
                'cons': ['Large size', 'Slow inference', 'High memory']
            },
            'ResNet-50': {
                'params': '25.6M',
                'inference_speed': 'Medium (80ms)',
                'accuracy_potential': 'Very Good (87-92%)',
                'memory_usage': 'Medium (102MB)',
                'best_for': 'Deep feature learning',
                'pros': ['Proven architecture', 'Deep features'],
                'cons': ['Larger than efficient alternatives']
            },
            'ResNet-101': {
                'params': '44.7M',
                'inference_speed': 'Slow (120ms)',
                'accuracy_potential': 'Excellent (89-94%)',
                'memory_usage': 'High (179MB)',
                'best_for': 'Complex plant distinctions',
                'pros': ['Very deep features', 'High accuracy'],
                'cons': ['Large size', 'Slow inference']
            }
        }
    
    @staticmethod
    def recommend_architecture(use_case: str) -> str:
        """Recommend best architecture based on use case"""
        recommendations = {
            'mobile_app': 'MobileNetV3-Large',
            'web_app': 'EfficientNet-B3',
            'research': 'EfficientNet-B7',
            'real_time': 'MobileNetV3-Small',
            'high_accuracy': 'EfficientNet-B7',
            'balanced': 'EfficientNet-B0',
            'server_deployment': 'EfficientNet-B3',
            'edge_device': 'MobileNetV3-Large'
        }
        return recommendations.get(use_case, 'EfficientNet-B3')

# Example usage and testing
if __name__ == "__main__":
    # Initialize model factory
    num_classes = 31  # Number of medicinal plant species
    model_factory = PlantRecognitionModels(num_classes)
    
    # Create different models
    print("🌿 Creating Plant Recognition Models...")
    
    # MobileNetV3 for mobile deployment
    mobile_model, mobile_base = model_factory.create_mobilenet_v3('large')
    print(f"📱 MobileNetV3-Large: {mobile_model.count_params():,} parameters")
    
    # EfficientNet for balanced performance
    efficient_model, efficient_base = model_factory.create_efficientnet('B3')
    print(f"⚡ EfficientNet-B3: {efficient_model.count_params():,} parameters")
    
    # ResNet for deep feature learning
    resnet_model, resnet_base = model_factory.create_resnet('50')
    print(f"🔬 ResNet-50: {resnet_model.count_params():,} parameters")
    
    # Display recommendations
    comparison = ModelComparison()
    print("\n📊 Model Recommendations:")
    print(f"Mobile App: {comparison.recommend_architecture('mobile_app')}")
    print(f"Web Application: {comparison.recommend_architecture('web_app')}")
    print(f"High Accuracy: {comparison.recommend_architecture('high_accuracy')}")
    print(f"Real-time: {comparison.recommend_architecture('real_time')}")
